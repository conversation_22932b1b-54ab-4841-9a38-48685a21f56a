#!/usr/bin/env python3
"""
Pure CloudFormation deployment script for Knowledge Retrieval System.
This script deploys the system using only CloudFormation without any CDK dependencies.
"""

import argparse
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import boto3
from botocore.exceptions import ClientError, WaiterError


class PureCloudFormationDeployer:
    """Handles pure CloudFormation deployment without CDK dependencies."""

    def __init__(self, region: str):
        """Initialize the deployer with AWS clients."""
        self.region = region
        self.cfn_client = boto3.client('cloudformation', region_name=region)
        self.s3_client = boto3.client('s3', region_name=region)

    def load_parameters(self, env: str, custom_params: Dict[str, str] = None) -> List[Dict[str, str]]:
        """Load parameters for the specified environment."""
        params_file = Path(f"infrastructure/cfn/parameters/{env}.json")
        
        if params_file.exists():
            with open(params_file, 'r') as f:
                params = json.load(f)
        else:
            raise FileNotFoundError(f"Parameter file not found: {params_file}")
        
        # Override with custom parameters
        if custom_params:
            param_dict = {p['ParameterKey']: p['ParameterValue'] for p in params}
            param_dict.update(custom_params)
            params = [{'ParameterKey': k, 'ParameterValue': v} for k, v in param_dict.items()]
        
        return params

    def create_artifacts_bucket(self, bucket_name: str) -> bool:
        """Create S3 bucket for CloudFormation artifacts."""
        try:
            # Check if bucket exists
            self.s3_client.head_bucket(Bucket=bucket_name)
            print(f"✓ S3 bucket already exists: {bucket_name}")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                # Bucket doesn't exist, create it
                try:
                    if self.region == 'us-east-1':
                        self.s3_client.create_bucket(Bucket=bucket_name)
                    else:
                        self.s3_client.create_bucket(
                            Bucket=bucket_name,
                            CreateBucketConfiguration={'LocationConstraint': self.region}
                        )
                    
                    # Enable versioning
                    self.s3_client.put_bucket_versioning(
                        Bucket=bucket_name,
                        VersioningConfiguration={'Status': 'Enabled'}
                    )
                    
                    # Set lifecycle policy
                    lifecycle_policy = {
                        'Rules': [
                            {
                                'ID': 'DeleteOldVersions',
                                'Status': 'Enabled',
                                'NoncurrentVersionExpiration': {'NoncurrentDays': 30},
                                'AbortIncompleteMultipartUpload': {'DaysAfterInitiation': 7}
                            }
                        ]
                    }
                    
                    self.s3_client.put_bucket_lifecycle_configuration(
                        Bucket=bucket_name,
                        LifecycleConfiguration=lifecycle_policy
                    )
                    
                    print(f"✓ Created S3 bucket: {bucket_name}")
                    return True
                    
                except ClientError as create_error:
                    print(f"✗ Failed to create S3 bucket: {create_error}")
                    return False
            else:
                print(f"✗ Error checking S3 bucket: {e}")
                return False

    def upload_lambda_code(self, bucket_name: str, env: str) -> bool:
        """Upload Lambda function code to S3."""
        print("📦 Uploading Lambda function code...")
        
        lambda_functions = ['reader', 'writer', 'ingestion']
        
        for func_name in lambda_functions:
            source_dir = Path(f"lambda/{func_name}")
            if not source_dir.exists():
                print(f"✗ Lambda source directory not found: {source_dir}")
                return False
            
            # Create zip file
            import zipfile
            import tempfile
            
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as tmp_file:
                with zipfile.ZipFile(tmp_file.name, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_path in source_dir.rglob('*'):
                        if file_path.is_file() and not file_path.name.startswith('.'):
                            arcname = file_path.relative_to(source_dir)
                            zip_file.write(file_path, arcname)
                
                # Upload to S3
                s3_key = f"lambda/{func_name}-{env}.zip"
                try:
                    self.s3_client.upload_file(tmp_file.name, bucket_name, s3_key)
                    print(f"✓ Uploaded {func_name} Lambda code to s3://{bucket_name}/{s3_key}")
                except ClientError as e:
                    print(f"✗ Failed to upload {func_name} Lambda code: {e}")
                    return False
                finally:
                    os.unlink(tmp_file.name)
        
        # Upload common layer
        layer_dir = Path("lambda/layers/common")
        if layer_dir.exists():
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as tmp_file:
                with zipfile.ZipFile(tmp_file.name, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_path in layer_dir.rglob('*'):
                        if file_path.is_file() and not file_path.name.startswith('.'):
                            arcname = file_path.relative_to(layer_dir)
                            zip_file.write(file_path, arcname)
                
                s3_key = f"layers/common-layer-{env}.zip"
                try:
                    self.s3_client.upload_file(tmp_file.name, bucket_name, s3_key)
                    print(f"✓ Uploaded common layer to s3://{bucket_name}/{s3_key}")
                except ClientError as e:
                    print(f"✗ Failed to upload common layer: {e}")
                    return False
                finally:
                    os.unlink(tmp_file.name)
        
        return True

    def validate_template(self, template_path: Path) -> bool:
        """Validate CloudFormation template."""
        try:
            with open(template_path, 'r') as f:
                template_body = f.read()
            
            self.cfn_client.validate_template(TemplateBody=template_body)
            print(f"✓ Template validation passed: {template_path.name}")
            return True
        except ClientError as e:
            print(f"✗ Template validation failed for {template_path.name}: {e}")
            return False

    def deploy_stack(self, stack_name: str, template_path: Path, 
                    parameters: List[Dict[str, str]], update: bool = False) -> bool:
        """Deploy CloudFormation stack."""
        print(f"🚀 {'Updating' if update else 'Creating'} stack: {stack_name}")
        
        with open(template_path, 'r') as f:
            template_body = f.read()

        cfn_params = {
            'StackName': stack_name,
            'TemplateBody': template_body,
            'Parameters': parameters,
            'Capabilities': ['CAPABILITY_IAM', 'CAPABILITY_NAMED_IAM'],
            'Tags': [
                {'Key': 'Project', 'Value': 'IntelligentKnowledgeRetrieval'},
                {'Key': 'Environment', 'Value': next((p['ParameterValue'] for p in parameters if p['ParameterKey'] == 'Environment'), 'unknown')},
                {'Key': 'DeploymentMethod', 'Value': 'PureCloudFormation'},
            ]
        }

        try:
            if update:
                response = self.cfn_client.update_stack(**cfn_params)
            else:
                response = self.cfn_client.create_stack(**cfn_params)
            
            stack_id = response['StackId']
            print(f"📋 Stack operation initiated: {stack_id}")
            
            return self.wait_for_stack_operation(stack_name, update)
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ValidationError' and 'No updates are to be performed' in str(e):
                print("ℹ️  No updates are required for the stack.")
                return True
            else:
                print(f"✗ Error {'updating' if update else 'creating'} stack: {e}")
                return False

    def wait_for_stack_operation(self, stack_name: str, is_update: bool) -> bool:
        """Wait for stack create/update operation to complete."""
        waiter_name = 'stack_update_complete' if is_update else 'stack_create_complete'
        waiter = self.cfn_client.get_waiter(waiter_name)
        
        print(f"⏳ Waiting for stack {'update' if is_update else 'creation'} to complete...")
        
        try:
            waiter.wait(
                StackName=stack_name,
                WaiterConfig={
                    'Delay': 30,
                    'MaxAttempts': 120  # 60 minutes max
                }
            )
            print(f"✅ Stack {'update' if is_update else 'creation'} completed successfully!")
            return True
            
        except WaiterError as e:
            print(f"✗ Stack operation failed: {e}")
            self.print_stack_events(stack_name)
            return False

    def print_stack_events(self, stack_name: str, limit: int = 10):
        """Print recent stack events for debugging."""
        try:
            response = self.cfn_client.describe_stack_events(StackName=stack_name)
            events = response['StackEvents'][:limit]
            
            print(f"\n📋 Recent stack events for {stack_name}:")
            print("-" * 80)
            for event in events:
                timestamp = event['Timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                resource_type = event.get('ResourceType', 'N/A')
                logical_id = event.get('LogicalResourceId', 'N/A')
                status = event.get('ResourceStatus', 'N/A')
                reason = event.get('ResourceStatusReason', '')
                
                print(f"{timestamp} | {resource_type} | {logical_id} | {status}")
                if reason:
                    print(f"  Reason: {reason}")
            print("-" * 80)
            
        except ClientError as e:
            print(f"Could not retrieve stack events: {e}")

    def get_stack_outputs(self, stack_name: str) -> Dict[str, str]:
        """Get stack outputs."""
        try:
            response = self.cfn_client.describe_stacks(StackName=stack_name)
            outputs = response['Stacks'][0].get('Outputs', [])
            return {output['OutputKey']: output['OutputValue'] for output in outputs}
        except ClientError:
            return {}

    def stack_exists(self, stack_name: str) -> bool:
        """Check if a CloudFormation stack exists."""
        try:
            self.cfn_client.describe_stacks(StackName=stack_name)
            return True
        except ClientError as e:
            if 'does not exist' in str(e):
                return False
            raise


def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description='Deploy Knowledge Retrieval System with pure CloudFormation')
    parser.add_argument('--env', required=True, help='Environment (dev/prod)')
    parser.add_argument('--stack-name', required=True, help='CloudFormation stack name')
    parser.add_argument('--region', required=True, help='AWS region')
    parser.add_argument('--update', action='store_true', help='Update existing stack')
    parser.add_argument('--slack-bot-token', required=True, help='Slack Bot Token')
    parser.add_argument('--slack-signing-secret', required=True, help='Slack Signing Secret')
    parser.add_argument('--amazon-q-app-id', help='Amazon Q Application ID (optional)')
    parser.add_argument('--vpc-id', help='Existing VPC ID (optional)')
    parser.add_argument('--private-subnet-ids', help='Private subnet IDs (comma-separated)')
    parser.add_argument('--public-subnet-ids', help='Public subnet IDs (comma-separated)')
    parser.add_argument('--existing-lambda-sg-id', help='Existing Lambda security group ID')
    parser.add_argument('--existing-opensearch-sg-id', help='Existing OpenSearch security group ID')

    args = parser.parse_args()

    # Get AWS account ID
    sts_client = boto3.client('sts', region_name=args.region)
    try:
        account_id = sts_client.get_caller_identity()['Account']
    except ClientError as e:
        print(f"✗ Failed to get AWS account ID: {e}")
        sys.exit(1)

    deployer = PureCloudFormationDeployer(args.region)

    try:
        print("🚀 Starting Pure CloudFormation Deployment")
        print("=" * 50)
        print(f"Environment: {args.env}")
        print(f"Stack Name: {args.stack_name}")
        print(f"Region: {args.region}")
        print(f"Account: {account_id}")
        print("=" * 50)

        # Step 1: Create S3 bucket for artifacts
        bucket_name = f"knowledge-retrieval-artifacts-{account_id}-{args.region}"
        if not deployer.create_artifacts_bucket(bucket_name):
            sys.exit(1)

        # Step 2: Upload Lambda code
        if not deployer.upload_lambda_code(bucket_name, args.env):
            sys.exit(1)

        # Step 3: Load and customize parameters
        custom_params = {
            'SlackBotToken': args.slack_bot_token,
            'SlackSigningSecret': args.slack_signing_secret,
        }

        if args.amazon_q_app_id:
            custom_params['AmazonQApplicationId'] = args.amazon_q_app_id

        if args.vpc_id:
            custom_params['VpcId'] = args.vpc_id

        if args.private_subnet_ids:
            custom_params['PrivateSubnetIds'] = args.private_subnet_ids

        if args.public_subnet_ids:
            custom_params['PublicSubnetIds'] = args.public_subnet_ids

        if args.existing_lambda_sg_id:
            custom_params['ExistingLambdaSecurityGroupId'] = args.existing_lambda_sg_id

        if args.existing_opensearch_sg_id:
            custom_params['ExistingOpenSearchSecurityGroupId'] = args.existing_opensearch_sg_id

        parameters = deployer.load_parameters(args.env, custom_params)
        print(f"📋 Loaded {len(parameters)} parameters for environment: {args.env}")

        # Step 4: Validate template
        template_path = Path("infrastructure/cfn/templates/knowledge-retrieval-main.yaml")
        if not template_path.exists():
            print(f"✗ Template not found: {template_path}")
            sys.exit(1)

        if not deployer.validate_template(template_path):
            sys.exit(1)

        # Step 5: Check if stack exists
        stack_exists = deployer.stack_exists(args.stack_name)

        if args.update and not stack_exists:
            print(f"✗ Stack {args.stack_name} does not exist. Cannot update.")
            sys.exit(1)

        if not args.update and stack_exists:
            print(f"✗ Stack {args.stack_name} already exists. Use --update to update it.")
            sys.exit(1)

        # Step 6: Deploy stack
        success = deployer.deploy_stack(
            args.stack_name,
            template_path,
            parameters,
            args.update
        )

        if success:
            # Print stack outputs
            outputs = deployer.get_stack_outputs(args.stack_name)
            if outputs:
                print("\n📊 Stack Outputs:")
                print("-" * 40)
                for key, value in outputs.items():
                    print(f"{key}: {value}")
                print("-" * 40)

            print(f"\n✅ Stack {args.stack_name} {'updated' if args.update else 'deployed'} successfully!")
            print("\n🎉 Next Steps:")
            print("1. Configure your Slack app with the SlackEventsEndpoint URL")
            print("2. Test the deployment by mentioning your bot in Slack")
            print("3. Monitor the system using the CloudWatch dashboard")
        else:
            print(f"\n❌ Stack {args.stack_name} {'update' if args.update else 'deployment'} failed!")
            sys.exit(1)

    except Exception as e:
        print(f"💥 Deployment failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
