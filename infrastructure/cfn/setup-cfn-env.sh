#!/bin/bash
# CloudFormation Environment Setup Script
# This script helps set up the environment for CloudFormation deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENV=${ENV:-dev}
AWS_REGION=${AWS_REGION:-us-east-1}

echo -e "${BLUE}CloudFormation Environment Setup${NC}"
echo "=================================="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if ! command_exists aws; then
    echo -e "${RED}Error: AWS CLI is not installed${NC}"
    echo "Please install AWS CLI: https://aws.amazon.com/cli/"
    exit 1
fi

if ! command_exists python3; then
    echo -e "${RED}Error: Python 3 is not installed${NC}"
    echo "Please install Python 3.8 or later"
    exit 1
fi

if ! command_exists node; then
    echo -e "${RED}Error: Node.js is not installed${NC}"
    echo "Please install Node.js for CDK: https://nodejs.org/"
    exit 1
fi

if ! command_exists cdk; then
    echo -e "${RED}Error: AWS CDK is not installed${NC}"
    echo "Please install CDK: npm install -g aws-cdk"
    exit 1
fi

echo -e "${GREEN}✓ All prerequisites are installed${NC}"

# Check AWS credentials
echo -e "${BLUE}Checking AWS credentials...${NC}"
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}Error: AWS credentials are not configured${NC}"
    echo "Please configure AWS credentials using:"
    echo "  aws configure"
    echo "  or set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables"
    exit 1
fi

AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
AWS_USER=$(aws sts get-caller-identity --query Arn --output text)

echo -e "${GREEN}✓ AWS Account: ${AWS_ACCOUNT}${NC}"
echo -e "${GREEN}✓ AWS Region: ${AWS_REGION}${NC}"
echo -e "${GREEN}✓ AWS User: ${AWS_USER}${NC}"

# Check required environment variables
echo -e "${BLUE}Checking environment variables...${NC}"

MISSING_VARS=()

if [ -z "${SLACK_BOT_TOKEN}" ]; then
    MISSING_VARS+=("SLACK_BOT_TOKEN")
fi

if [ -z "${SLACK_SIGNING_SECRET}" ]; then
    MISSING_VARS+=("SLACK_SIGNING_SECRET")
fi

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo -e "${RED}Error: Missing required environment variables:${NC}"
    for var in "${MISSING_VARS[@]}"; do
        echo -e "${RED}  - ${var}${NC}"
    done
    echo ""
    echo -e "${YELLOW}Please set these variables:${NC}"
    echo "export SLACK_BOT_TOKEN=xoxb-your-bot-token"
    echo "export SLACK_SIGNING_SECRET=your-signing-secret"
    echo "export AMAZON_Q_APPLICATION_ID=your-amazon-q-app-id  # Optional"
    exit 1
fi

echo -e "${GREEN}✓ Required environment variables are set${NC}"

# Install Python dependencies
echo -e "${BLUE}Installing Python dependencies...${NC}"
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt >/dev/null 2>&1
    echo -e "${GREEN}✓ Python dependencies installed${NC}"
else
    echo -e "${YELLOW}Warning: requirements.txt not found${NC}"
fi

# Install boto3 if not present
if ! python3 -c "import boto3" >/dev/null 2>&1; then
    echo -e "${BLUE}Installing boto3...${NC}"
    pip3 install boto3 >/dev/null 2>&1
    echo -e "${GREEN}✓ boto3 installed${NC}"
fi

# Create CloudFormation directories
echo -e "${BLUE}Setting up CloudFormation directories...${NC}"
mkdir -p infrastructure/cfn/templates
mkdir -p infrastructure/cfn/packaged
mkdir -p infrastructure/cfn/parameters

echo -e "${GREEN}✓ CloudFormation directories created${NC}"

# Make deployment script executable
chmod +x infrastructure/cfn/deploy-cfn.py

# Create S3 bucket for CloudFormation artifacts
PROJECT_NAME="knowledge-retrieval-system"
BUCKET_NAME="${PROJECT_NAME}-cfn-artifacts-${AWS_ACCOUNT}-${AWS_REGION}"

echo -e "${BLUE}Setting up S3 bucket for CloudFormation artifacts...${NC}"
if aws s3 ls "s3://${BUCKET_NAME}" >/dev/null 2>&1; then
    echo -e "${GREEN}✓ S3 bucket already exists: ${BUCKET_NAME}${NC}"
else
    if aws s3 mb "s3://${BUCKET_NAME}" --region "${AWS_REGION}" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ S3 bucket created: ${BUCKET_NAME}${NC}"
        
        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "${BUCKET_NAME}" \
            --versioning-configuration Status=Enabled >/dev/null 2>&1
        
        # Set lifecycle policy to clean up old versions
        cat > /tmp/lifecycle-policy.json << EOF
{
    "Rules": [
        {
            "ID": "DeleteOldVersions",
            "Status": "Enabled",
            "NoncurrentVersionExpiration": {
                "NoncurrentDays": 30
            },
            "AbortIncompleteMultipartUpload": {
                "DaysAfterInitiation": 7
            }
        }
    ]
}
EOF
        
        aws s3api put-bucket-lifecycle-configuration \
            --bucket "${BUCKET_NAME}" \
            --lifecycle-configuration file:///tmp/lifecycle-policy.json >/dev/null 2>&1
        
        rm /tmp/lifecycle-policy.json
        
        echo -e "${GREEN}✓ S3 bucket configured with lifecycle policy${NC}"
    else
        echo -e "${RED}Error: Failed to create S3 bucket${NC}"
        exit 1
    fi
fi

# Check CDK bootstrap
echo -e "${BLUE}Checking CDK bootstrap...${NC}"
if aws cloudformation describe-stacks --stack-name "CDKToolkit" --region "${AWS_REGION}" >/dev/null 2>&1; then
    echo -e "${GREEN}✓ CDK is already bootstrapped${NC}"
else
    echo -e "${YELLOW}CDK is not bootstrapped. Run 'make bootstrap' to bootstrap CDK${NC}"
fi

# Summary
echo ""
echo -e "${GREEN}✅ CloudFormation environment setup completed!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Generate CloudFormation templates:"
echo "   make cfn-generate ENV=${ENV}"
echo ""
echo "2. Validate templates:"
echo "   make cfn-validate ENV=${ENV}"
echo ""
echo "3. Deploy with CloudFormation:"
echo "   make cfn-deploy ENV=${ENV} AWS_ACCOUNT=${AWS_ACCOUNT} AWS_REGION=${AWS_REGION}"
echo ""
echo -e "${BLUE}Or use the all-in-one command:${NC}"
echo "   make all-cfn ENV=${ENV} AWS_ACCOUNT=${AWS_ACCOUNT} AWS_REGION=${AWS_REGION}"
echo ""
echo -e "${YELLOW}Configuration:${NC}"
echo "  Environment: ${ENV}"
echo "  AWS Account: ${AWS_ACCOUNT}"
echo "  AWS Region: ${AWS_REGION}"
echo "  S3 Bucket: ${BUCKET_NAME}"
