# Pure CloudFormation Deployment

This directory contains **pure CloudFormation templates** for deploying the Knowledge Retrieval System **without any CDK dependencies**. This approach is ideal for enterprise environments, CI/CD pipelines, and situations where CDK bootstrap is not allowed or preferred.

## 🎯 Key Benefits

✅ **No CDK Bootstrap Required** - Deploy directly with CloudFormation  
✅ **No Node.js Dependencies** - Only requires AWS CLI and Python  
✅ **Standard AWS Tooling** - Uses native CloudFormation APIs  
✅ **Enterprise Ready** - Suitable for strict enterprise environments  
✅ **CI/CD Friendly** - Integrates with any CloudFormation-aware pipeline  
✅ **Template Visibility** - All templates are explicit and auditable  

## 📁 Directory Structure

```
infrastructure/cfn/
├── README.md                           # This file
├── templates/                          # CloudFormation templates
│   ├── knowledge-retrieval-main.yaml   # Main stack (orchestrates nested stacks)
│   ├── network-stack.yaml             # VPC, subnets, security groups
│   ├── storage-stack.yaml             # DynamoDB, S3, SQS, OpenSearch
│   ├── compute-stack.yaml             # Lambda functions, API Gateway
│   └── monitoring-stack.yaml          # CloudWatch alarms, dashboard
├── parameters/                         # Environment-specific parameters
│   ├── dev.json                       # Development environment
│   └── prod.json                      # Production environment
└── scripts/
    └── deploy.py                      # Deployment automation script
```

## 🚀 Quick Start

### Prerequisites
- **AWS CLI** configured with appropriate permissions
- **Python 3.8+** with boto3 installed
- **Required environment variables** set

### 1. Set Environment Variables
```bash
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************
export AWS_REGION=us-east-1
```

### 2. Deploy
```bash
# Deploy to development environment
make cfn-deploy ENV=dev

# Deploy to production environment
make cfn-deploy ENV=prod
```

### 3. Verify Deployment
```bash
# Check stack status
make cfn-status ENV=dev

# View stack outputs
make cfn-outputs ENV=dev
```

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `make cfn-validate` | Validate CloudFormation templates |
| `make cfn-deploy ENV=dev` | Deploy stack |
| `make cfn-update ENV=dev` | Update existing stack |
| `make cfn-destroy ENV=dev` | Destroy stack |
| `make cfn-status ENV=dev` | Show stack status |
| `make cfn-outputs ENV=dev` | Display stack outputs |
| `make all-cfn ENV=dev` | Complete deployment (build + deploy) |

## 🏗️ Architecture

The CloudFormation deployment uses a **nested stack architecture**:

```
Main Stack (knowledge-retrieval-main.yaml)
├── Network Stack (network-stack.yaml)
│   ├── VPC (optional - can use existing)
│   ├── Subnets (public/private)
│   ├── Security Groups
│   └── VPC Endpoints
├── Storage Stack (storage-stack.yaml)
│   ├── DynamoDB Table
│   ├── S3 Bucket
│   ├── SQS FIFO Queue
│   └── OpenSearch Domain
├── Compute Stack (compute-stack.yaml)
│   ├── Lambda Functions (Reader, Writer, Ingestion)
│   ├── Lambda Layers
│   ├── API Gateway
│   └── IAM Roles
└── Monitoring Stack (monitoring-stack.yaml)
    ├── CloudWatch Alarms
    ├── CloudWatch Dashboard
    └── SNS Topic
```

## ⚙️ Configuration

### Parameter Files
Environment-specific parameters are stored in JSON files:

**Development (`parameters/dev.json`):**
```json
[
  {
    "ParameterKey": "Environment",
    "ParameterValue": "dev"
  },
  {
    "ParameterKey": "LambdaMemorySize",
    "ParameterValue": "512"
  },
  {
    "ParameterKey": "OpenSearchInstanceType",
    "ParameterValue": "t3.small.search"
  }
]
```

**Production (`parameters/prod.json`):**
```json
[
  {
    "ParameterKey": "Environment",
    "ParameterValue": "prod"
  },
  {
    "ParameterKey": "LambdaMemorySize",
    "ParameterValue": "1024"
  },
  {
    "ParameterKey": "OpenSearchInstanceType",
    "ParameterValue": "m6g.large.search"
  }
]
```

### Environment Variables

**Required:**
```bash
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_SIGNING_SECRET=your-signing-secret
AWS_ACCOUNT=************
```

**Optional:**
```bash
AWS_REGION=us-east-1
AMAZON_Q_APPLICATION_ID=your-amazon-q-app-id
```

**For Existing VPC:**
```bash
VPC_ID=vpc-********
PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890
PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij
EXISTING_LAMBDA_SG_ID=sg-lambda123
EXISTING_OPENSEARCH_SG_ID=sg-opensearch456
```

## 🔧 Deployment Process

The deployment script (`scripts/deploy.py`) performs these steps:

1. **Validate AWS Credentials** - Ensures proper AWS access
2. **Create S3 Artifacts Bucket** - For Lambda code storage
3. **Upload Lambda Code** - Packages and uploads function code
4. **Load Parameters** - Merges environment-specific parameters
5. **Validate Templates** - Checks CloudFormation syntax
6. **Deploy Stack** - Creates/updates CloudFormation stack
7. **Monitor Progress** - Waits for completion and reports status

## 🏢 Enterprise Features

### VPC Support
- **New VPC Creation** - Automatically creates VPC with best practices
- **Existing VPC Integration** - Uses existing VPC and subnets
- **Security Groups** - Creates or uses existing security groups
- **VPC Endpoints** - Optional VPC endpoints for AWS services

### Security
- **IAM Roles** - Least privilege access for all resources
- **Encryption** - At-rest encryption for all data stores
- **Network Isolation** - Private subnets for Lambda functions
- **Security Groups** - Minimal required access rules

### Monitoring
- **CloudWatch Alarms** - Error rates, duration, and latency monitoring
- **Dashboard** - Comprehensive system overview
- **SNS Notifications** - Alert notifications for critical issues
- **Log Retention** - Configurable log retention periods

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Deploy with CloudFormation
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Deploy with CloudFormation
        run: make cfn-deploy ENV=prod
        env:
          AWS_ACCOUNT: ${{ secrets.AWS_ACCOUNT }}
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}
```

### AWS CodePipeline Integration
The CloudFormation templates can be used directly in AWS CodePipeline with CloudFormation actions for create/update stack operations.

## 🛠️ Troubleshooting

### Common Issues

**1. Template Validation Errors**
```bash
# Validate all templates
make cfn-validate

# Validate specific template
aws cloudformation validate-template --template-body file://templates/main.yaml
```

**2. Stack Deployment Failures**
```bash
# Check stack status
make cfn-status ENV=dev

# View stack events
aws cloudformation describe-stack-events --stack-name your-stack-name
```

**3. Lambda Code Upload Issues**
```bash
# Check S3 bucket permissions
aws s3 ls s3://knowledge-retrieval-artifacts-ACCOUNT-REGION/

# Manually upload Lambda code
python3 scripts/deploy.py --env dev --stack-name test-stack --region us-east-1 \
  --slack-bot-token "token" --slack-signing-secret "secret"
```

**4. Parameter Issues**
```bash
# Check parameter file syntax
cat parameters/dev.json | jq .

# Validate parameter values
aws cloudformation validate-template --template-body file://templates/main.yaml \
  --parameters file://parameters/dev.json
```

### Debug Commands
```bash
# View CloudFormation events
aws cloudformation describe-stack-events --stack-name knowledge-retrieval-system-dev

# Check stack resources
aws cloudformation describe-stack-resources --stack-name knowledge-retrieval-system-dev

# View stack outputs
aws cloudformation describe-stacks --stack-name knowledge-retrieval-system-dev \
  --query 'Stacks[0].Outputs'
```

## 📊 Monitoring and Maintenance

### CloudWatch Dashboard
Access the dashboard at:
```
https://console.aws.amazon.com/cloudwatch/home?region=REGION#dashboards:name=KnowledgeRetrieval-ENV
```

### Key Metrics to Monitor
- **Lambda Invocations** - Function execution counts
- **Lambda Errors** - Error rates and types
- **Lambda Duration** - Execution time trends
- **API Gateway 4XX/5XX** - Client and server errors
- **API Gateway Latency** - Response time performance

### Maintenance Tasks
```bash
# Update stack with latest changes
make cfn-update ENV=prod

# Check stack drift
aws cloudformation detect-stack-drift --stack-name knowledge-retrieval-system-prod

# View drift results
aws cloudformation describe-stack-drift-detection-status --stack-drift-detection-id ID
```

## 🔄 Migration from CDK

If migrating from CDK deployment:

1. **Export CDK outputs** to compare with CloudFormation
2. **Deploy CloudFormation stack** with same configuration
3. **Verify all functionality** works identically
4. **Update DNS/endpoints** if needed
5. **Destroy CDK stack** once verified

## 📞 Support

For CloudFormation-specific issues:
1. Check AWS CloudFormation documentation
2. Review template validation errors
3. Check CloudWatch logs for Lambda functions
4. Use AWS CloudFormation console for detailed error messages
5. Verify IAM permissions for deployment user/role
