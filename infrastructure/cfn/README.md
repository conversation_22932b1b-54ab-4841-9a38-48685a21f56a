# CloudFormation Deployment Option

This directory contains CloudFormation deployment capabilities for the Knowledge Retrieval System. The CloudFormation templates are generated from the existing CDK code, providing an alternative deployment method for environments where CDK is not available or preferred.

## Overview

The CloudFormation deployment option provides:

- **Template Generation**: Automatically generates CloudFormation templates from CDK code
- **Parameter Management**: Environment-specific parameter files
- **Deployment Scripts**: Python-based deployment automation
- **Validation**: Template validation before deployment
- **Packaging**: Handles S3 asset packaging for Lambda functions

## Directory Structure

```
infrastructure/cfn/
├── README.md                    # This file
├── deploy-cfn.py               # Main deployment script
├── parameters/                 # Environment-specific parameters
│   ├── dev.json               # Development environment parameters
│   └── prod.json              # Production environment parameters
├── templates/                 # Generated CloudFormation templates (auto-created)
└── packaged/                  # Packaged templates with S3 references (auto-created)
```

## Prerequisites

1. **AWS CLI**: Configured with appropriate permissions
2. **Python 3.8+**: With boto3 installed
3. **CDK**: For template generation (same as regular CDK deployment)
4. **S3 Bucket**: Will be auto-created for CloudFormation artifacts

## Quick Start

### 1. Generate CloudFormation Templates

```bash
# Generate templates for development environment
make cfn-generate ENV=dev

# Generate templates for production environment
make cfn-generate ENV=prod
```

### 2. Validate Templates

```bash
# Validate generated templates
make cfn-validate ENV=dev
```

### 3. Deploy Stack

```bash
# Deploy to development environment
make cfn-deploy ENV=dev AWS_ACCOUNT=********9 AWS_REGION=us-east-1

# Deploy to production environment
make cfn-deploy ENV=prod AWS_ACCOUNT=********9 AWS_REGION=us-west-2
```

## Available Make Targets

| Target | Description |
|--------|-------------|
| `cfn-generate` | Generate CloudFormation templates from CDK |
| `cfn-validate` | Validate CloudFormation templates |
| `cfn-package` | Package templates with S3 assets |
| `cfn-deploy` | Deploy using CloudFormation |
| `cfn-update` | Update existing CloudFormation stack |
| `cfn-destroy` | Destroy CloudFormation stack |
| `cfn-status` | Show CloudFormation stack status |
| `all-cfn` | Clean, build, and deploy with CloudFormation |

## Environment Configuration

### Parameter Files

Environment-specific parameters are stored in JSON files:

- `parameters/dev.json`: Development environment settings
- `parameters/prod.json`: Production environment settings

### Required Environment Variables

Set these environment variables before deployment:

```bash
export AWS_ACCOUNT=********9012
export AWS_REGION=us-east-1
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AMAZON_Q_APPLICATION_ID=your-amazon-q-app-id  # Optional
```

### Optional VPC Configuration

For existing VPC deployment:

```bash
export VPC_ID=vpc-********
export EXISTING_LAMBDA_SG_ID=sg-lambda123
export EXISTING_OPENSEARCH_SG_ID=sg-opensearch456
```

## Deployment Process

The CloudFormation deployment follows these steps:

1. **Environment Setup**: Loads environment variables and parameters
2. **Template Generation**: CDK synthesizes CloudFormation templates
3. **Validation**: Templates are validated for syntax and structure
4. **Packaging**: Lambda code and assets are uploaded to S3
5. **Deployment**: CloudFormation stack is created/updated
6. **Monitoring**: Deployment progress is monitored and reported

## Comparison with CDK Deployment

| Aspect | CDK Deployment | CloudFormation Deployment |
|--------|----------------|---------------------------|
| **Ease of Use** | Simpler for developers | More explicit control |
| **Environment Support** | Requires Node.js/CDK | Standard AWS CLI only |
| **Template Visibility** | Generated on-the-fly | Explicit templates available |
| **Debugging** | CDK-specific tools | Standard CloudFormation tools |
| **CI/CD Integration** | CDK-aware pipelines | Standard CloudFormation pipelines |
| **Rollback** | CDK managed | CloudFormation managed |

## Troubleshooting

### Common Issues

1. **Template Generation Fails**
   ```bash
   # Ensure CDK dependencies are installed
   cd infrastructure && npm install
   
   # Check environment configuration
   make validate-config ENV=dev
   ```

2. **S3 Bucket Access Issues**
   ```bash
   # Ensure AWS credentials have S3 permissions
   aws s3 ls
   
   # Check bucket creation permissions
   aws iam get-user
   ```

3. **Stack Deployment Fails**
   ```bash
   # Check CloudFormation events
   make cfn-status ENV=dev
   
   # View detailed stack events in AWS Console
   ```

### Debugging Commands

```bash
# View generated templates
ls -la infrastructure/cfn/templates/

# Check template syntax
aws cloudformation validate-template --template-body file://infrastructure/cfn/templates/template.json

# Monitor stack events
aws cloudformation describe-stack-events --stack-name knowledge-retrieval-system-dev

# View stack resources
aws cloudformation describe-stack-resources --stack-name knowledge-retrieval-system-dev
```

## Security Considerations

- **IAM Permissions**: Ensure deployment user has necessary CloudFormation and service permissions
- **S3 Bucket**: Artifacts bucket is created with appropriate security settings
- **VPC Security**: Same security groups and VPC configuration as CDK deployment
- **Secrets Management**: Slack tokens and secrets are passed as parameters (consider AWS Secrets Manager)

## Migration Between Deployment Methods

### From CDK to CloudFormation

1. Export current CDK stack outputs
2. Generate CloudFormation templates
3. Deploy CloudFormation stack with same configuration
4. Verify functionality
5. Destroy CDK stack (optional)

### From CloudFormation to CDK

1. Export CloudFormation stack outputs
2. Deploy CDK stack with same configuration
3. Verify functionality
4. Destroy CloudFormation stack (optional)

## Support

For issues specific to CloudFormation deployment:

1. Check the deployment logs
2. Verify AWS permissions
3. Validate template syntax
4. Review parameter configuration
5. Check AWS CloudFormation console for detailed error messages
