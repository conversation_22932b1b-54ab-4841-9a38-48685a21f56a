AWSTemplateFormatVersion: '2010-09-09'
Description: 'Knowledge Retrieval System - Storage Stack (DynamoDB, S3, SQS, OpenSearch)'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  VpcId:
    Type: String
    Description: VPC ID

  LambdaSecurityGroupId:
    Type: String
    Description: Lambda security group ID

  OpenSearchSecurityGroupId:
    Type: String
    Description: OpenSearch security group ID

  PrivateSubnetIds:
    Type: String
    Description: Private subnet IDs (comma-separated)

  DynamoDBBillingMode:
    Type: String
    Default: PAY_PER_REQUEST
    AllowedValues: [PAY_PER_REQUEST, PROVISIONED]
    Description: DynamoDB billing mode

  DynamoDBTTLDays:
    Type: Number
    Default: 7
    Description: DynamoDB TTL in days

  OpenSearchInstanceType:
    Type: String
    Default: t3.small.search
    Description: OpenSearch instance type

  OpenSearchInstanceCount:
    Type: Number
    Default: 1
    Description: Number of OpenSearch instances

  SQSVisibilityTimeout:
    Type: Number
    Default: 180
    Description: SQS visibility timeout in seconds

  SQSRetentionPeriod:
    Type: Number
    Default: 1209600
    Description: SQS message retention period in seconds

Conditions:
  IsProvisioned: !Equals [!Ref DynamoDBBillingMode, 'PROVISIONED']

Resources:
  # S3 Bucket for document storage
  DocumentBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'knowledge-retrieval-documents-${Environment}-${AWS::AccountId}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteIncompleteMultipartUploads
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 7
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 30
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-documents-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # DynamoDB Table for conversation history
  ConversationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub 'knowledge-retrieval-conversations-${Environment}'
      BillingMode: !Ref DynamoDBBillingMode
      AttributeDefinitions:
        - AttributeName: conversation_id
          AttributeType: S
        - AttributeName: timestamp
          AttributeType: S
      KeySchema:
        - AttributeName: conversation_id
          KeyType: HASH
        - AttributeName: timestamp
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      ProvisionedThroughput: !If
        - IsProvisioned
        - ReadCapacityUnits: 5
          WriteCapacityUnits: 5
        - !Ref AWS::NoValue
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      SSESpecification:
        SSEEnabled: true
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-conversations-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # SQS FIFO Queue for message processing
  MessageQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'knowledge-retrieval-messages-${Environment}.fifo'
      FifoQueue: true
      ContentBasedDeduplication: true
      VisibilityTimeoutSeconds: !Ref SQSVisibilityTimeout
      MessageRetentionPeriod: !Ref SQSRetentionPeriod
      ReceiveMessageWaitTimeSeconds: 20
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-messages-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # OpenSearch Domain
  OpenSearchDomain:
    Type: AWS::OpenSearch::Domain
    Properties:
      DomainName: !Sub 'knowledge-retrieval-${Environment}'
      EngineVersion: 'OpenSearch_2.3'
      ClusterConfig:
        InstanceType: !Ref OpenSearchInstanceType
        InstanceCount: !Ref OpenSearchInstanceCount
        DedicatedMasterEnabled: false
        ZoneAwarenessEnabled: !If
          - MultiAZ
          - true
          - false
      EBSOptions:
        EBSEnabled: true
        VolumeType: gp3
        VolumeSize: 20
      VPCOptions:
        SecurityGroupIds:
          - !Ref OpenSearchSecurityGroupId
        SubnetIds: !Split [',', !Ref PrivateSubnetIds]
      EncryptionAtRestOptions:
        Enabled: true
      NodeToNodeEncryptionOptions:
        Enabled: true
      DomainEndpointOptions:
        EnforceHTTPS: true
        TLSSecurityPolicy: 'Policy-Min-TLS-1-2-2019-07'
      AccessPolicies:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${AWS::AccountId}:root'
            Action: 'es:*'
            Resource: !Sub 'arn:aws:es:${AWS::Region}:${AWS::AccountId}:domain/knowledge-retrieval-${Environment}/*'
      AdvancedOptions:
        'rest.action.multi.allow_explicit_index': 'true'
        'indices.fielddata.cache.size': '20'
        'indices.query.bool.max_clause_count': '1024'
      LogPublishingOptions:
        SEARCH_SLOW_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpenSearchSlowLogGroup.Arn
          Enabled: true
        INDEX_SLOW_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpenSearchIndexLogGroup.Arn
          Enabled: true
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Log Groups for OpenSearch
  OpenSearchSlowLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/opensearch/domains/knowledge-retrieval-${Environment}/search-slow-logs'
      RetentionInDays: 7

  OpenSearchIndexLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/opensearch/domains/knowledge-retrieval-${Environment}/index-slow-logs'
      RetentionInDays: 7

  # IAM Role for OpenSearch access
  OpenSearchAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'OpenSearchAccess-${Environment}-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
      Policies:
        - PolicyName: OpenSearchAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - es:ESHttpGet
                  - es:ESHttpPost
                  - es:ESHttpPut
                  - es:ESHttpDelete
                  - es:ESHttpHead
                Resource: !Sub 'arn:aws:es:${AWS::Region}:${AWS::AccountId}:domain/knowledge-retrieval-${Environment}/*'
      Tags:
        - Key: Name
          Value: !Sub 'OpenSearchAccess-${Environment}'
        - Key: Environment
          Value: !Ref Environment

Conditions:
  MultiAZ: !Not [!Equals [!Ref OpenSearchInstanceCount, 1]]

Outputs:
  DocumentBucketName:
    Description: S3 bucket name for document storage
    Value: !Ref DocumentBucket

  DocumentBucketArn:
    Description: S3 bucket ARN for document storage
    Value: !GetAtt DocumentBucket.Arn

  ConversationTableName:
    Description: DynamoDB table name for conversation history
    Value: !Ref ConversationTable

  ConversationTableArn:
    Description: DynamoDB table ARN for conversation history
    Value: !GetAtt ConversationTable.Arn

  MessageQueueUrl:
    Description: SQS FIFO queue URL for message processing
    Value: !Ref MessageQueue

  MessageQueueArn:
    Description: SQS FIFO queue ARN for message processing
    Value: !GetAtt MessageQueue.Arn

  OpenSearchDomainEndpoint:
    Description: OpenSearch domain endpoint
    Value: !GetAtt OpenSearchDomain.DomainEndpoint

  OpenSearchDomainArn:
    Description: OpenSearch domain ARN
    Value: !GetAtt OpenSearchDomain.DomainArn

  OpenSearchAccessRoleArn:
    Description: IAM role ARN for OpenSearch access
    Value: !GetAtt OpenSearchAccessRole.Arn
