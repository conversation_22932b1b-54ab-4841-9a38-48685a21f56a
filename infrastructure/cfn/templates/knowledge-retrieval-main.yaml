AWSTemplateFormatVersion: '2010-09-09'
Description: 'Knowledge Retrieval System - Main Stack'

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

  # Slack Configuration
  SlackBotToken:
    Type: String
    NoEcho: true
    Description: Slack Bot <PERSON>ken (xoxb-...)

  SlackSigningSecret:
    Type: String
    NoEcho: true
    Description: Slack Signing Secret

  # Amazon Q Configuration (Optional)
  AmazonQApplicationId:
    Type: String
    Default: ''
    Description: Amazon Q Business Application ID (optional)

  # Lambda Configuration
  LambdaMemorySize:
    Type: Number
    Default: 512
    MinValue: 128
    MaxValue: 10240
    Description: Lambda function memory size in MB

  LambdaTimeout:
    Type: Number
    Default: 30
    MinValue: 3
    MaxValue: 900
    Description: Lambda function timeout in seconds

  # Bedrock Configuration
  BedrockModelId:
    Type: String
    Default: 'anthropic.claude-3-sonnet-20240229-v1:0'
    Description: Bedrock model ID for text generation

  EmbeddingModelId:
    Type: String
    Default: 'amazon.titan-embed-text-v1'
    Description: Bedrock model ID for embeddings

  # DynamoDB Configuration
  DynamoDBBillingMode:
    Type: String
    Default: PAY_PER_REQUEST
    AllowedValues: [PAY_PER_REQUEST, PROVISIONED]
    Description: DynamoDB billing mode

  DynamoDBTTLDays:
    Type: Number
    Default: 7
    MinValue: 1
    MaxValue: 365
    Description: DynamoDB TTL in days

  # OpenSearch Configuration
  OpenSearchInstanceType:
    Type: String
    Default: t3.small.search
    Description: OpenSearch instance type

  OpenSearchInstanceCount:
    Type: Number
    Default: 1
    MinValue: 1
    MaxValue: 20
    Description: Number of OpenSearch instances

  # VPC Configuration (Optional)
  VpcId:
    Type: String
    Default: ''
    Description: Existing VPC ID (leave empty to create new VPC)

  PrivateSubnetIds:
    Type: CommaDelimitedList
    Default: ''
    Description: Existing private subnet IDs (comma-separated)

  PublicSubnetIds:
    Type: CommaDelimitedList
    Default: ''
    Description: Existing public subnet IDs (comma-separated)

  # Security Group Configuration (Optional)
  ExistingLambdaSecurityGroupId:
    Type: String
    Default: ''
    Description: Existing Lambda security group ID

  ExistingOpenSearchSecurityGroupId:
    Type: String
    Default: ''
    Description: Existing OpenSearch security group ID

  # SQS Configuration
  SQSVisibilityTimeout:
    Type: Number
    Default: 180
    MinValue: 0
    MaxValue: 43200
    Description: SQS visibility timeout in seconds

  SQSRetentionPeriod:
    Type: Number
    Default: 1209600
    MinValue: 60
    MaxValue: 1209600
    Description: SQS message retention period in seconds

Conditions:
  CreateNewVPC: !Equals [!Ref VpcId, '']
  UseExistingVPC: !Not [!Equals [!Ref VpcId, '']]
  CreateLambdaSecurityGroup: !Equals [!Ref ExistingLambdaSecurityGroupId, '']
  CreateOpenSearchSecurityGroup: !Equals [!Ref ExistingOpenSearchSecurityGroupId, '']
  HasAmazonQApplicationId: !Not [!Equals [!Ref AmazonQApplicationId, '']]

Resources:
  # Network Stack
  NetworkStack:
    Type: AWS::CloudFormation::Stack
    Properties:
      TemplateURL: ./network-stack.yaml
      Parameters:
        Environment: !Ref Environment
        VpcId: !Ref VpcId
        PrivateSubnetIds: !Join [',', !Ref PrivateSubnetIds]
        PublicSubnetIds: !Join [',', !Ref PublicSubnetIds]
        ExistingLambdaSecurityGroupId: !Ref ExistingLambdaSecurityGroupId
        ExistingOpenSearchSecurityGroupId: !Ref ExistingOpenSearchSecurityGroupId

  # Storage Stack
  StorageStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: NetworkStack
    Properties:
      TemplateURL: ./storage-stack.yaml
      Parameters:
        Environment: !Ref Environment
        VpcId: !GetAtt NetworkStack.Outputs.VpcId
        LambdaSecurityGroupId: !GetAtt NetworkStack.Outputs.LambdaSecurityGroupId
        OpenSearchSecurityGroupId: !GetAtt NetworkStack.Outputs.OpenSearchSecurityGroupId
        PrivateSubnetIds: !GetAtt NetworkStack.Outputs.PrivateSubnetIds
        DynamoDBBillingMode: !Ref DynamoDBBillingMode
        DynamoDBTTLDays: !Ref DynamoDBTTLDays
        OpenSearchInstanceType: !Ref OpenSearchInstanceType
        OpenSearchInstanceCount: !Ref OpenSearchInstanceCount
        SQSVisibilityTimeout: !Ref SQSVisibilityTimeout
        SQSRetentionPeriod: !Ref SQSRetentionPeriod

  # Compute Stack
  ComputeStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: [NetworkStack, StorageStack]
    Properties:
      TemplateURL: ./compute-stack.yaml
      Parameters:
        Environment: !Ref Environment
        VpcId: !GetAtt NetworkStack.Outputs.VpcId
        LambdaSecurityGroupId: !GetAtt NetworkStack.Outputs.LambdaSecurityGroupId
        PrivateSubnetIds: !GetAtt NetworkStack.Outputs.PrivateSubnetIds
        DocumentBucketName: !GetAtt StorageStack.Outputs.DocumentBucketName
        ConversationTableName: !GetAtt StorageStack.Outputs.ConversationTableName
        ConversationTableArn: !GetAtt StorageStack.Outputs.ConversationTableArn
        OpenSearchDomainEndpoint: !GetAtt StorageStack.Outputs.OpenSearchDomainEndpoint
        OpenSearchDomainArn: !GetAtt StorageStack.Outputs.OpenSearchDomainArn
        MessageQueueUrl: !GetAtt StorageStack.Outputs.MessageQueueUrl
        MessageQueueArn: !GetAtt StorageStack.Outputs.MessageQueueArn
        SlackBotToken: !Ref SlackBotToken
        SlackSigningSecret: !Ref SlackSigningSecret
        AmazonQApplicationId: !Ref AmazonQApplicationId
        LambdaMemorySize: !Ref LambdaMemorySize
        LambdaTimeout: !Ref LambdaTimeout
        BedrockModelId: !Ref BedrockModelId
        EmbeddingModelId: !Ref EmbeddingModelId

  # Monitoring Stack
  MonitoringStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: [ComputeStack]
    Properties:
      TemplateURL: ./monitoring-stack.yaml
      Parameters:
        Environment: !Ref Environment
        ReaderLambdaFunctionName: !GetAtt ComputeStack.Outputs.ReaderLambdaFunctionName
        WriterLambdaFunctionName: !GetAtt ComputeStack.Outputs.WriterLambdaFunctionName
        IngestionLambdaFunctionName: !GetAtt ComputeStack.Outputs.IngestionLambdaFunctionName
        APIGatewayName: !GetAtt ComputeStack.Outputs.APIGatewayName

Outputs:
  # Network Outputs
  VpcId:
    Description: VPC ID
    Value: !GetAtt NetworkStack.Outputs.VpcId
    Export:
      Name: !Sub '${AWS::StackName}-VpcId'

  # Storage Outputs
  DynamoDBTableName:
    Description: DynamoDB table for conversation history
    Value: !GetAtt StorageStack.Outputs.ConversationTableName
    Export:
      Name: !Sub '${AWS::StackName}-DynamoDBTableName'

  OpenSearchDomainEndpoint:
    Description: OpenSearch domain endpoint for document search
    Value: !GetAtt StorageStack.Outputs.OpenSearchDomainEndpoint
    Export:
      Name: !Sub '${AWS::StackName}-OpenSearchDomainEndpoint'

  DocumentBucketName:
    Description: S3 bucket for document storage
    Value: !GetAtt StorageStack.Outputs.DocumentBucketName
    Export:
      Name: !Sub '${AWS::StackName}-DocumentBucketName'

  SQSQueueUrl:
    Description: SQS FIFO queue URL for message processing
    Value: !GetAtt StorageStack.Outputs.MessageQueueUrl
    Export:
      Name: !Sub '${AWS::StackName}-SQSQueueUrl'

  # Compute Outputs
  APIGatewayEndpoint:
    Description: API Gateway endpoint for Slack integration
    Value: !GetAtt ComputeStack.Outputs.APIGatewayEndpoint
    Export:
      Name: !Sub '${AWS::StackName}-APIGatewayEndpoint'

  SlackEventsEndpoint:
    Description: Slack Events API endpoint for webhook configuration
    Value: !Sub '${ComputeStack.Outputs.APIGatewayEndpoint}slack/events'
    Export:
      Name: !Sub '${AWS::StackName}-SlackEventsEndpoint'

  SlackCommandsEndpoint:
    Description: Slack Slash Commands endpoint for /askq command configuration
    Value: !Sub '${ComputeStack.Outputs.APIGatewayEndpoint}slack/commands'
    Export:
      Name: !Sub '${AWS::StackName}-SlackCommandsEndpoint'

  TestQueryEndpoint:
    Description: Direct API test endpoint for testing without Slack integration
    Value: !Sub '${ComputeStack.Outputs.APIGatewayEndpoint}test/query'
    Export:
      Name: !Sub '${AWS::StackName}-TestQueryEndpoint'
