AWSTemplateFormatVersion: '2010-09-09'
Description: 'Knowledge Retrieval System - Compute Stack (Lambda, API Gateway)'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  VpcId:
    Type: String
    Description: VPC ID

  LambdaSecurityGroupId:
    Type: String
    Description: Lambda security group ID

  PrivateSubnetIds:
    Type: String
    Description: Private subnet IDs (comma-separated)

  DocumentBucketName:
    Type: String
    Description: S3 bucket name for document storage

  ConversationTableName:
    Type: String
    Description: DynamoDB table name for conversation history

  ConversationTableArn:
    Type: String
    Description: DynamoDB table ARN for conversation history

  OpenSearchDomainEndpoint:
    Type: String
    Description: OpenSearch domain endpoint

  OpenSearchDomainArn:
    Type: String
    Description: OpenSearch domain ARN

  MessageQueueUrl:
    Type: String
    Description: SQS queue URL

  MessageQueueArn:
    Type: String
    Description: SQS queue ARN

  SlackBotToken:
    Type: String
    NoEcho: true
    Description: Slack Bot Token

  SlackSigningSecret:
    Type: String
    NoEcho: true
    Description: Slack Signing Secret

  AmazonQApplicationId:
    Type: String
    Default: ''
    Description: Amazon Q Application ID (optional)

  LambdaMemorySize:
    Type: Number
    Default: 512
    Description: Lambda function memory size in MB

  LambdaTimeout:
    Type: Number
    Default: 30
    Description: Lambda function timeout in seconds

  BedrockModelId:
    Type: String
    Default: 'anthropic.claude-3-sonnet-20240229-v1:0'
    Description: Bedrock model ID for text generation

  EmbeddingModelId:
    Type: String
    Default: 'amazon.titan-embed-text-v1'
    Description: Bedrock model ID for embeddings

Conditions:
  HasAmazonQApplicationId: !Not [!Equals [!Ref AmazonQApplicationId, '']]

Resources:
  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'KnowledgeRetrievalLambdaRole-${Environment}-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                Resource: !Ref ConversationTableArn
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !Sub 'arn:aws:s3:::${DocumentBucketName}'
                  - !Sub 'arn:aws:s3:::${DocumentBucketName}/*'
        - PolicyName: SQSAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sqs:SendMessage
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:GetQueueAttributes
                Resource: !Ref MessageQueueArn
        - PolicyName: OpenSearchAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - es:ESHttpGet
                  - es:ESHttpPost
                  - es:ESHttpPut
                  - es:ESHttpDelete
                  - es:ESHttpHead
                Resource: !Sub '${OpenSearchDomainArn}/*'
        - PolicyName: BedrockAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - bedrock:InvokeModel
                  - bedrock:InvokeModelWithResponseStream
                Resource:
                  - !Sub 'arn:aws:bedrock:${AWS::Region}::foundation-model/${BedrockModelId}'
                  - !Sub 'arn:aws:bedrock:${AWS::Region}::foundation-model/${EmbeddingModelId}'
        - PolicyName: AmazonQAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - qbusiness:ChatSync
                  - qbusiness:GetApplication
                  - qbusiness:ListConversations
                  - qbusiness:GetConversation
                Resource: !If
                  - HasAmazonQApplicationId
                  - !Sub 'arn:aws:qbusiness:${AWS::Region}:${AWS::AccountId}:application/${AmazonQApplicationId}'
                  - !Ref AWS::NoValue
      Tags:
        - Key: Name
          Value: !Sub 'KnowledgeRetrievalLambdaRole-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Lambda Layer for common dependencies
  CommonLayer:
    Type: AWS::Lambda::LayerVersion
    Properties:
      LayerName: !Sub 'knowledge-retrieval-common-${Environment}'
      Description: Common dependencies for Knowledge Retrieval Lambda functions
      Content:
        S3Bucket: !Sub 'knowledge-retrieval-artifacts-${AWS::AccountId}-${AWS::Region}'
        S3Key: !Sub 'layers/common-layer-${Environment}.zip'
      CompatibleRuntimes:
        - python3.11
      CompatibleArchitectures:
        - x86_64

  # Reader Lambda Function (Slack event processing)
  ReaderLambdaFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub 'knowledge-retrieval-reader-${Environment}'
      Runtime: python3.11
      Handler: app.handler
      Code:
        S3Bucket: !Sub 'knowledge-retrieval-artifacts-${AWS::AccountId}-${AWS::Region}'
        S3Key: !Sub 'lambda/reader-${Environment}.zip'
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Role: !GetAtt LambdaExecutionRole.Arn
      Environment:
        Variables:
          QUEUE_URL: !Ref MessageQueueUrl
          ENVIRONMENT: !Ref Environment
          SLACK_BOT_TOKEN: !Ref SlackBotToken
          SLACK_SIGNING_SECRET: !Ref SlackSigningSecret
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroupId
        SubnetIds: !Split [',', !Ref PrivateSubnetIds]
      Layers:
        - !Ref CommonLayer
      ReservedConcurrencyLimit: 10
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-reader-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Reader Lambda Log Group
  ReaderLambdaLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/knowledge-retrieval-reader-${Environment}'
      RetentionInDays: 7

  # Writer Lambda Function (Knowledge retrieval processing)
  WriterLambdaFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub 'knowledge-retrieval-writer-${Environment}'
      Runtime: python3.11
      Handler: app.handler
      Code:
        S3Bucket: !Sub 'knowledge-retrieval-artifacts-${AWS::AccountId}-${AWS::Region}'
        S3Key: !Sub 'lambda/writer-${Environment}.zip'
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Role: !GetAtt LambdaExecutionRole.Arn
      Environment:
        Variables:
          DYNAMODB_TABLE: !Ref ConversationTableName
          OPENSEARCH_DOMAIN: !Ref OpenSearchDomainEndpoint
          BEDROCK_MODEL_ID: !Ref BedrockModelId
          EMBEDDING_MODEL_ID: !Ref EmbeddingModelId
          TTL_DAYS: '7'
          ENVIRONMENT: !Ref Environment
          AMAZON_Q_APPLICATION_ID: !If [HasAmazonQApplicationId, !Ref AmazonQApplicationId, '']
          SLACK_BOT_TOKEN: !Ref SlackBotToken
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroupId
        SubnetIds: !Split [',', !Ref PrivateSubnetIds]
      Layers:
        - !Ref CommonLayer
      ReservedConcurrencyLimit: 5
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-writer-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Writer Lambda Log Group
  WriterLambdaLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/knowledge-retrieval-writer-${Environment}'
      RetentionInDays: 7

  # Ingestion Lambda Function (Document processing)
  IngestionLambdaFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub 'knowledge-retrieval-ingestion-${Environment}'
      Runtime: python3.11
      Handler: app.handler
      Code:
        S3Bucket: !Sub 'knowledge-retrieval-artifacts-${AWS::AccountId}-${AWS::Region}'
        S3Key: !Sub 'lambda/ingestion-${Environment}.zip'
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Role: !GetAtt LambdaExecutionRole.Arn
      Environment:
        Variables:
          DOCUMENT_BUCKET: !Ref DocumentBucketName
          OPENSEARCH_DOMAIN: !Ref OpenSearchDomainEndpoint
          EMBEDDING_MODEL_ID: !Ref EmbeddingModelId
          ENVIRONMENT: !Ref Environment
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroupId
        SubnetIds: !Split [',', !Ref PrivateSubnetIds]
      Layers:
        - !Ref CommonLayer
      ReservedConcurrencyLimit: 3
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-ingestion-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Ingestion Lambda Log Group
  IngestionLambdaLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/knowledge-retrieval-ingestion-${Environment}'
      RetentionInDays: 7

  # API Gateway
  APIGateway:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: !Sub 'knowledge-retrieval-api-${Environment}'
      Description: API Gateway for Slack Knowledge Retrieval System
      EndpointConfiguration:
        Types:
          - REGIONAL
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-api-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # API Gateway Deployment
  APIGatewayDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - SlackEventsMethod
      - SlackInteractiveMethod
      - SlackCommandsMethod
      - TestQueryMethod
    Properties:
      RestApiId: !Ref APIGateway
      StageName: v1
      StageDescription:
        LoggingLevel: INFO
        DataTraceEnabled: true
        MetricsEnabled: true

  # API Gateway CloudWatch Role
  APIGatewayCloudWatchRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: apigateway.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs

  # API Gateway Account
  APIGatewayAccount:
    Type: AWS::ApiGateway::Account
    Properties:
      CloudWatchRoleArn: !GetAtt APIGatewayCloudWatchRole.Arn

  # Lambda Permission for API Gateway
  ReaderLambdaAPIGatewayPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref ReaderLambdaFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub '${APIGateway}/*/POST/*'

  # API Gateway Resources and Methods
  SlackResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !GetAtt APIGateway.RootResourceId
      PathPart: slack

  # Slack Events Resource
  SlackEventsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !Ref SlackResource
      PathPart: events

  SlackEventsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref APIGateway
      ResourceId: !Ref SlackEventsResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ReaderLambdaFunction.Arn}/invocations'
      MethodResponses:
        - StatusCode: 200
        - StatusCode: 400
        - StatusCode: 500

  # Slack Interactive Resource
  SlackInteractiveResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !Ref SlackResource
      PathPart: interactive

  SlackInteractiveMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref APIGateway
      ResourceId: !Ref SlackInteractiveResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ReaderLambdaFunction.Arn}/invocations'
      MethodResponses:
        - StatusCode: 200

  # Slack Commands Resource
  SlackCommandsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !Ref SlackResource
      PathPart: commands

  SlackCommandsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref APIGateway
      ResourceId: !Ref SlackCommandsResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ReaderLambdaFunction.Arn}/invocations'
      MethodResponses:
        - StatusCode: 200

  # Test Resource
  TestResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !GetAtt APIGateway.RootResourceId
      PathPart: test

  TestQueryResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref APIGateway
      ParentId: !Ref TestResource
      PathPart: query

  TestQueryMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref APIGateway
      ResourceId: !Ref TestQueryResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ReaderLambdaFunction.Arn}/invocations'
      MethodResponses:
        - StatusCode: 200

Outputs:
  ReaderLambdaFunctionName:
    Description: Reader Lambda function name
    Value: !Ref ReaderLambdaFunction

  WriterLambdaFunctionName:
    Description: Writer Lambda function name
    Value: !Ref WriterLambdaFunction

  IngestionLambdaFunctionName:
    Description: Ingestion Lambda function name
    Value: !Ref IngestionLambdaFunction

  APIGatewayName:
    Description: API Gateway name
    Value: !Ref APIGateway

  APIGatewayEndpoint:
    Description: API Gateway endpoint URL
    Value: !Sub 'https://${APIGateway}.execute-api.${AWS::Region}.amazonaws.com/v1/'
