AWSTemplateFormatVersion: '2010-09-09'
Description: 'Knowledge Retrieval System - Network Stack (VPC, Security Groups)'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  VpcId:
    Type: String
    Default: ''
    Description: Existing VPC ID (leave empty to create new VPC)

  PrivateSubnetIds:
    Type: String
    Default: ''
    Description: Existing private subnet IDs (comma-separated)

  PublicSubnetIds:
    Type: String
    Default: ''
    Description: Existing public subnet IDs (comma-separated)

  ExistingLambdaSecurityGroupId:
    Type: String
    Default: ''
    Description: Existing Lambda security group ID

  ExistingOpenSearchSecurityGroupId:
    Type: String
    Default: ''
    Description: Existing OpenSearch security group ID

Conditions:
  CreateNewVPC: !Equals [!Ref VpcId, '']
  UseExistingVPC: !Not [!Equals [!Ref VpcId, '']]
  CreateLambdaSecurityGroup: !Equals [!Ref ExistingLambdaSecurityGroupId, '']
  CreateOpenSearchSecurityGroup: !Equals [!Ref ExistingOpenSearchSecurityGroupId, '']

Resources:
  # VPC Resources (only created if VpcId is not provided)
  VPC:
    Type: AWS::EC2::VPC
    Condition: CreateNewVPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-vpc-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Condition: CreateNewVPC
    Properties:
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-igw-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Condition: CreateNewVPC
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  # Public Subnets
  PublicSubnet1:
    Type: AWS::EC2::Subnet
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-public-subnet-1-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  PublicSubnet2:
    Type: AWS::EC2::Subnet
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [1, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-public-subnet-2-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Private Subnets
  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: *********/24
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-private-subnet-1-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [1, !GetAZs '']
      CidrBlock: *********/24
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-private-subnet-2-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # NAT Gateway
  NatGateway1EIP:
    Type: AWS::EC2::EIP
    Condition: CreateNewVPC
    DependsOn: InternetGatewayAttachment
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-nat-eip-1-${Environment}'

  NatGateway1:
    Type: AWS::EC2::NatGateway
    Condition: CreateNewVPC
    Properties:
      AllocationId: !GetAtt NatGateway1EIP.AllocationId
      SubnetId: !Ref PublicSubnet1
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-nat-1-${Environment}'

  # Route Tables
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-public-rt-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  DefaultPublicRoute:
    Type: AWS::EC2::Route
    Condition: CreateNewVPC
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  PublicSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Condition: CreateNewVPC
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet1

  PublicSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Condition: CreateNewVPC
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet2

  PrivateRouteTable1:
    Type: AWS::EC2::RouteTable
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-private-rt-1-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  DefaultPrivateRoute1:
    Type: AWS::EC2::Route
    Condition: CreateNewVPC
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId: !Ref NatGateway1

  PrivateSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Condition: CreateNewVPC
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      SubnetId: !Ref PrivateSubnet1

  PrivateSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Condition: CreateNewVPC
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      SubnetId: !Ref PrivateSubnet2

  # VPC Endpoints (only for new VPC)
  S3GatewayEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.s3'
      VpcEndpointType: Gateway
      RouteTableIds:
        - !Ref PrivateRouteTable1

  DynamoDBGatewayEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Condition: CreateNewVPC
    Properties:
      VpcId: !Ref VPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.dynamodb'
      VpcEndpointType: Gateway
      RouteTableIds:
        - !Ref PrivateRouteTable1

  # Security Groups
  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: CreateLambdaSecurityGroup
    Properties:
      GroupDescription: !Sub 'Security group for Lambda functions - ${Environment}'
      GroupName: !Sub 'lambda-sg-${Environment}'
      VpcId: !If [CreateNewVPC, !Ref VPC, !Ref VpcId]
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: 'HTTPS outbound for AWS services (Bedrock, DynamoDB, S3, SQS)'
        - IpProtocol: tcp
          FromPort: 9200
          ToPort: 9200
          DestinationSecurityGroupId: !If 
            - CreateOpenSearchSecurityGroup
            - !Ref OpenSearchSecurityGroup
            - !Ref ExistingOpenSearchSecurityGroupId
          Description: 'OpenSearch access'
      Tags:
        - Key: Name
          Value: !Sub 'lambda-sg-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  OpenSearchSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: CreateOpenSearchSecurityGroup
    Properties:
      GroupDescription: !Sub 'Security group for OpenSearch domain - ${Environment}'
      GroupName: !Sub 'opensearch-sg-${Environment}'
      VpcId: !If [CreateNewVPC, !Ref VPC, !Ref VpcId]
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !If 
            - CreateLambdaSecurityGroup
            - !Ref LambdaSecurityGroup
            - !Ref ExistingLambdaSecurityGroupId
          Description: 'HTTPS access from Lambda functions'
        - IpProtocol: tcp
          FromPort: 9200
          ToPort: 9200
          SourceSecurityGroupId: !If 
            - CreateLambdaSecurityGroup
            - !Ref LambdaSecurityGroup
            - !Ref ExistingLambdaSecurityGroupId
          Description: 'OpenSearch access from Lambda functions'
      Tags:
        - Key: Name
          Value: !Sub 'opensearch-sg-${Environment}'
        - Key: Environment
          Value: !Ref Environment

Outputs:
  VpcId:
    Description: VPC ID
    Value: !If [CreateNewVPC, !Ref VPC, !Ref VpcId]

  PrivateSubnetIds:
    Description: Private subnet IDs
    Value: !If 
      - CreateNewVPC
      - !Sub '${PrivateSubnet1},${PrivateSubnet2}'
      - !Ref PrivateSubnetIds

  PublicSubnetIds:
    Description: Public subnet IDs
    Value: !If 
      - CreateNewVPC
      - !Sub '${PublicSubnet1},${PublicSubnet2}'
      - !Ref PublicSubnetIds

  LambdaSecurityGroupId:
    Description: Lambda security group ID
    Value: !If 
      - CreateLambdaSecurityGroup
      - !Ref LambdaSecurityGroup
      - !Ref ExistingLambdaSecurityGroupId

  OpenSearchSecurityGroupId:
    Description: OpenSearch security group ID
    Value: !If 
      - CreateOpenSearchSecurityGroup
      - !Ref OpenSearchSecurityGroup
      - !Ref ExistingOpenSearchSecurityGroupId
