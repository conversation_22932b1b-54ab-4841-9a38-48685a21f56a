AWSTemplateFormatVersion: '2010-09-09'
Description: 'Knowledge Retrieval System - Monitoring Stack (CloudWatch Alarms, Dashboard)'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ReaderLambdaFunctionName:
    Type: String
    Description: Reader Lambda function name

  WriterLambdaFunctionName:
    Type: String
    Description: Writer Lambda function name

  IngestionLambdaFunctionName:
    Type: String
    Description: Ingestion Lambda function name

  APIGatewayName:
    Type: String
    Description: API Gateway name

Resources:
  # SNS Topic for alarm notifications
  AlarmTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub 'knowledge-retrieval-alarms-${Environment}'
      DisplayName: !Sub 'Knowledge Retrieval Alarms - ${Environment}'
      Tags:
        - Key: Name
          Value: !Sub 'knowledge-retrieval-alarms-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Dashboard
  KnowledgeRetrievalDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub 'KnowledgeRetrieval-${Environment}'
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "text",
              "x": 0,
              "y": 0,
              "width": 24,
              "height": 1,
              "properties": {
                "markdown": "# Knowledge Retrieval System - ${Environment.toUpperCase()}"
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 1,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/Lambda", "Invocations", "FunctionName", "${ReaderLambdaFunctionName}" ],
                  [ ".", "Errors", ".", "." ],
                  [ ".", "Duration", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Reader Lambda Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 1,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/Lambda", "Invocations", "FunctionName", "${WriterLambdaFunctionName}" ],
                  [ ".", "Errors", ".", "." ],
                  [ ".", "Duration", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Writer Lambda Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 7,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/Lambda", "Invocations", "FunctionName", "${IngestionLambdaFunctionName}" ],
                  [ ".", "Errors", ".", "." ],
                  [ ".", "Duration", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Ingestion Lambda Metrics",
                "period": 300
              }
            },
            {
              "type": "text",
              "x": 0,
              "y": 13,
              "width": 24,
              "height": 1,
              "properties": {
                "markdown": "## API Gateway Metrics"
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 14,
              "width": 24,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ApiGateway", "Count", "ApiName", "${APIGatewayName}" ],
                  [ ".", "4XXError", ".", "." ],
                  [ ".", "5XXError", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "API Gateway HTTP Status Codes",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 20,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ApiGateway", "Latency", "ApiName", "${APIGatewayName}" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "API Gateway Latency",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 20,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ApiGateway", "IntegrationLatency", "ApiName", "${APIGatewayName}" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "API Gateway Integration Latency",
                "period": 300
              }
            }
          ]
        }

  # Lambda Function Alarms
  ReaderLambdaErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ReaderLambdaFunctionName}-errors'
      AlarmDescription: !Sub 'Error rate alarm for ${ReaderLambdaFunctionName}'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref ReaderLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  ReaderLambdaDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ReaderLambdaFunctionName}-duration'
      AlarmDescription: !Sub 'Duration alarm for ${ReaderLambdaFunctionName}'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 24000  # 80% of 30 seconds
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref ReaderLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  WriterLambdaErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${WriterLambdaFunctionName}-errors'
      AlarmDescription: !Sub 'Error rate alarm for ${WriterLambdaFunctionName}'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref WriterLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  WriterLambdaDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${WriterLambdaFunctionName}-duration'
      AlarmDescription: !Sub 'Duration alarm for ${WriterLambdaFunctionName}'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 48000  # 80% of 60 seconds
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref WriterLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  IngestionLambdaErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${IngestionLambdaFunctionName}-errors'
      AlarmDescription: !Sub 'Error rate alarm for ${IngestionLambdaFunctionName}'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 3
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref IngestionLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  IngestionLambdaDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${IngestionLambdaFunctionName}-duration'
      AlarmDescription: !Sub 'Duration alarm for ${IngestionLambdaFunctionName}'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 24000  # 80% of 30 seconds
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: FunctionName
          Value: !Ref IngestionLambdaFunctionName
      AlarmActions:
        - !Ref AlarmTopic

  # API Gateway Alarms
  APIGateway5xxAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'APIGateway-5xx-${Environment}'
      AlarmDescription: !Sub '5XX error alarm for API Gateway - ${Environment}'
      MetricName: 5XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: ApiName
          Value: !Ref APIGatewayName
      AlarmActions:
        - !Ref AlarmTopic

  APIGateway4xxAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'APIGateway-4xx-${Environment}'
      AlarmDescription: !Sub '4XX error alarm for API Gateway - ${Environment}'
      MetricName: 4XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 20
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: ApiName
          Value: !Ref APIGatewayName
      AlarmActions:
        - !Ref AlarmTopic

  APIGatewayLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'APIGateway-latency-${Environment}'
      AlarmDescription: !Sub 'High latency alarm for API Gateway - ${Environment}'
      MetricName: Latency
      Namespace: AWS/ApiGateway
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 2
      Threshold: 10000  # 10 seconds
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: notBreaching
      Dimensions:
        - Name: ApiName
          Value: !Ref APIGatewayName
      AlarmActions:
        - !Ref AlarmTopic

Outputs:
  AlarmTopicArn:
    Description: SNS topic ARN for alarm notifications
    Value: !Ref AlarmTopic

  DashboardURL:
    Description: CloudWatch Dashboard URL
    Value: !Sub 'https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${KnowledgeRetrievalDashboard}'
