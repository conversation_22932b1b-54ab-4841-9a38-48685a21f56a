#!/usr/bin/env python3
"""
CloudFormation deployment script for Knowledge Retrieval System.
This script deploys the CDK-generated CloudFormation templates.
"""

import argparse
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import boto3
from botocore.exceptions import ClientError, WaiterError


class CloudFormationDeployer:
    """Handles CloudFormation stack deployment operations."""

    def __init__(self, region: str):
        """Initialize the deployer with AWS clients."""
        self.region = region
        self.cfn_client = boto3.client('cloudformation', region_name=region)
        self.s3_client = boto3.client('s3', region_name=region)

    def load_parameters(self, env: str) -> Dict[str, str]:
        """Load parameters for the specified environment."""
        params_file = Path(f"infrastructure/cfn/parameters/{env}.json")
        if params_file.exists():
            with open(params_file, 'r') as f:
                return json.load(f)
        
        # Fallback to environment variables
        env_file = Path(f"infrastructure/env/.env.{env}")
        params = {}
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        params[key] = value
        return params

    def get_template_files(self, templates_dir: str) -> List[Path]:
        """Get all CloudFormation template files in the directory."""
        templates_path = Path(templates_dir)
        if not templates_path.exists():
            raise FileNotFoundError(f"Templates directory not found: {templates_dir}")
        
        template_files = []
        for pattern in ['*.yaml', '*.yml', '*.json']:
            template_files.extend(templates_path.glob(pattern))
        
        if not template_files:
            raise FileNotFoundError(f"No CloudFormation templates found in {templates_dir}")
        
        return sorted(template_files)

    def stack_exists(self, stack_name: str) -> bool:
        """Check if a CloudFormation stack exists."""
        try:
            self.cfn_client.describe_stacks(StackName=stack_name)
            return True
        except ClientError as e:
            if 'does not exist' in str(e):
                return False
            raise

    def get_stack_status(self, stack_name: str) -> Optional[str]:
        """Get the current status of a CloudFormation stack."""
        try:
            response = self.cfn_client.describe_stacks(StackName=stack_name)
            return response['Stacks'][0]['StackStatus']
        except ClientError:
            return None

    def format_parameters(self, params: Dict[str, str]) -> List[Dict[str, str]]:
        """Format parameters for CloudFormation API."""
        return [
            {'ParameterKey': key, 'ParameterValue': value}
            for key, value in params.items()
        ]

    def deploy_stack(self, stack_name: str, template_file: Path,
                    parameters: Dict[str, str], update: bool = False) -> bool:
        """Deploy or update a CloudFormation stack."""
        print(f"{'Updating' if update else 'Creating'} stack: {stack_name}")
        print(f"Template: {template_file}")

        with open(template_file, 'r') as f:
            template_body = f.read()

        cfn_params = {
            'StackName': stack_name,
            'TemplateBody': template_body,
            'Parameters': self.format_parameters(parameters),
            'Capabilities': ['CAPABILITY_IAM', 'CAPABILITY_NAMED_IAM'],
            'Tags': [
                {'Key': 'Project', 'Value': 'IntelligentKnowledgeRetrieval'},
                {'Key': 'Environment', 'Value': parameters.get('ENV', 'dev')},
                {'Key': 'DeploymentMethod', 'Value': 'CloudFormation'},
            ]
        }

        try:
            if update:
                response = self.cfn_client.update_stack(**cfn_params)
            else:
                response = self.cfn_client.create_stack(**cfn_params)

            stack_id = response['StackId']
            print(f"Stack operation initiated: {stack_id}")

            return self.wait_for_stack_operation(stack_name, update)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ValidationError' and 'No updates are to be performed' in str(e):
                print("No updates are required for the stack.")
                return True
            else:
                print(f"Error {'updating' if update else 'creating'} stack: {e}")
                return False

    def deploy_multiple_stacks(self, stack_base_name: str, templates_dir: str,
                              parameters: Dict[str, str], update: bool = False) -> bool:
        """Deploy multiple related stacks in the correct order."""
        template_files = self.get_template_files(templates_dir)

        # Define deployment order based on dependencies
        # Main stack should be deployed first, then nested stacks
        deployment_order = []
        main_template = None
        nested_templates = []

        for template_file in template_files:
            template_name = template_file.stem.lower()
            if 'knowledgeretrievalstack' in template_name or template_name == stack_base_name.lower():
                main_template = template_file
            else:
                nested_templates.append(template_file)

        # Deploy main stack first
        if main_template:
            deployment_order.append((f"{stack_base_name}", main_template))

        # Then deploy nested stacks
        for template_file in nested_templates:
            template_name = template_file.stem
            stack_name = f"{stack_base_name}-{template_name}"
            deployment_order.append((stack_name, template_file))

        # Deploy stacks in order
        for stack_name, template_file in deployment_order:
            print(f"\n{'='*60}")
            print(f"Deploying stack: {stack_name}")
            print(f"{'='*60}")

            success = self.deploy_stack(stack_name, template_file, parameters, update)
            if not success:
                print(f"Failed to deploy stack: {stack_name}")
                return False

            print(f"✅ Successfully deployed: {stack_name}")

        return True

    def wait_for_stack_operation(self, stack_name: str, is_update: bool) -> bool:
        """Wait for stack create/update operation to complete."""
        waiter_name = 'stack_update_complete' if is_update else 'stack_create_complete'
        waiter = self.cfn_client.get_waiter(waiter_name)
        
        print(f"Waiting for stack {'update' if is_update else 'creation'} to complete...")
        
        try:
            waiter.wait(
                StackName=stack_name,
                WaiterConfig={
                    'Delay': 30,
                    'MaxAttempts': 120  # 60 minutes max
                }
            )
            print(f"Stack {'update' if is_update else 'creation'} completed successfully!")
            return True
            
        except WaiterError as e:
            print(f"Stack operation failed: {e}")
            self.print_stack_events(stack_name)
            return False

    def print_stack_events(self, stack_name: str, limit: int = 10):
        """Print recent stack events for debugging."""
        try:
            response = self.cfn_client.describe_stack_events(StackName=stack_name)
            events = response['StackEvents'][:limit]
            
            print(f"\nRecent stack events for {stack_name}:")
            print("-" * 80)
            for event in events:
                timestamp = event['Timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                resource_type = event.get('ResourceType', 'N/A')
                logical_id = event.get('LogicalResourceId', 'N/A')
                status = event.get('ResourceStatus', 'N/A')
                reason = event.get('ResourceStatusReason', '')
                
                print(f"{timestamp} | {resource_type} | {logical_id} | {status}")
                if reason:
                    print(f"  Reason: {reason}")
            print("-" * 80)
            
        except ClientError as e:
            print(f"Could not retrieve stack events: {e}")

    def get_stack_outputs(self, stack_name: str) -> Dict[str, str]:
        """Get stack outputs."""
        try:
            response = self.cfn_client.describe_stacks(StackName=stack_name)
            outputs = response['Stacks'][0].get('Outputs', [])
            return {output['OutputKey']: output['OutputValue'] for output in outputs}
        except ClientError:
            return {}


def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description='Deploy CloudFormation templates')
    parser.add_argument('--env', required=True, help='Environment (dev/prod)')
    parser.add_argument('--stack-name', required=True, help='CloudFormation stack name')
    parser.add_argument('--region', required=True, help='AWS region')
    parser.add_argument('--templates-dir', required=True, help='Directory containing templates')
    parser.add_argument('--update', action='store_true', help='Update existing stack')
    
    args = parser.parse_args()
    
    deployer = CloudFormationDeployer(args.region)
    
    try:
        # Load parameters
        parameters = deployer.load_parameters(args.env)
        print(f"Loaded {len(parameters)} parameters for environment: {args.env}")
        
        # Get template files
        template_files = deployer.get_template_files(args.templates_dir)
        print(f"Found {len(template_files)} template files")
        
        # Check if stack exists
        stack_exists = deployer.stack_exists(args.stack_name)
        
        if args.update and not stack_exists:
            print(f"Error: Stack {args.stack_name} does not exist. Cannot update.")
            sys.exit(1)
        
        if not args.update and stack_exists:
            print(f"Stack {args.stack_name} already exists. Use --update to update it.")
            sys.exit(1)
        
        # Deploy stacks (handles both single and multiple templates)
        if len(template_files) == 1:
            # Single template deployment
            success = deployer.deploy_stack(
                args.stack_name,
                template_files[0],
                parameters,
                args.update
            )
        else:
            # Multiple templates deployment (nested stacks)
            success = deployer.deploy_multiple_stacks(
                args.stack_name,
                args.templates_dir,
                parameters,
                args.update
            )
        
        if success:
            # Print stack outputs
            outputs = deployer.get_stack_outputs(args.stack_name)
            if outputs:
                print("\nStack Outputs:")
                for key, value in outputs.items():
                    print(f"  {key}: {value}")
            
            print(f"\n✅ Stack {args.stack_name} {'updated' if args.update else 'deployed'} successfully!")
        else:
            print(f"\n❌ Stack {args.stack_name} {'update' if args.update else 'deployment'} failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"Deployment failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
