[{"ParameterKey": "Environment", "ParameterValue": "dev"}, {"ParameterKey": "LambdaMemorySize", "ParameterValue": "512"}, {"ParameterKey": "LambdaTimeout", "ParameterValue": "30"}, {"ParameterKey": "OpenSearchInstanceType", "ParameterValue": "t3.small.search"}, {"ParameterKey": "OpenSearchInstanceCount", "ParameterValue": "1"}, {"ParameterKey": "DynamoDBBillingMode", "ParameterValue": "PAY_PER_REQUEST"}, {"ParameterKey": "DynamoDBTTLDays", "ParameterValue": "7"}, {"ParameterKey": "BedrockModelId", "ParameterValue": "anthropic.claude-3-sonnet-20240229-v1:0"}, {"ParameterKey": "EmbeddingModelId", "ParameterValue": "amazon.titan-embed-text-v1"}, {"ParameterKey": "SQSVisibilityTimeout", "ParameterValue": "180"}, {"ParameterKey": "SQSRetentionPeriod", "ParameterValue": "1209600"}, {"ParameterKey": "VpcId", "ParameterValue": ""}, {"ParameterKey": "PrivateSubnetIds", "ParameterValue": ""}, {"ParameterKey": "PublicSubnetIds", "ParameterValue": ""}, {"ParameterKey": "ExistingLambdaSecurityGroupId", "ParameterValue": ""}, {"ParameterKey": "ExistingOpenSearchSecurityGroupId", "ParameterValue": ""}, {"ParameterKey": "AmazonQApplicationId", "ParameterValue": ""}]