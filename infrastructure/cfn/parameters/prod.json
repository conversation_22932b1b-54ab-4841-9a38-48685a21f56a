[{"ParameterKey": "Environment", "ParameterValue": "prod"}, {"ParameterKey": "LambdaMemorySize", "ParameterValue": "1024"}, {"ParameterKey": "LambdaTimeout", "ParameterValue": "60"}, {"ParameterKey": "OpenSearchInstanceType", "ParameterValue": "m6g.large.search"}, {"ParameterKey": "OpenSearchInstanceCount", "ParameterValue": "2"}, {"ParameterKey": "DynamoDBBillingMode", "ParameterValue": "PAY_PER_REQUEST"}, {"ParameterKey": "DynamoDBTTLDays", "ParameterValue": "30"}, {"ParameterKey": "BedrockModelId", "ParameterValue": "anthropic.claude-3-sonnet-20240229-v1:0"}, {"ParameterKey": "EmbeddingModelId", "ParameterValue": "amazon.titan-embed-text-v1"}, {"ParameterKey": "SQSVisibilityTimeout", "ParameterValue": "300"}, {"ParameterKey": "SQSRetentionPeriod", "ParameterValue": "1209600"}, {"ParameterKey": "VpcId", "ParameterValue": ""}, {"ParameterKey": "PrivateSubnetIds", "ParameterValue": ""}, {"ParameterKey": "PublicSubnetIds", "ParameterValue": ""}, {"ParameterKey": "ExistingLambdaSecurityGroupId", "ParameterValue": ""}, {"ParameterKey": "ExistingOpenSearchSecurityGroupId", "ParameterValue": ""}, {"ParameterKey": "AmazonQApplicationId", "ParameterValue": ""}]