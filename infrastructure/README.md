# Infrastructure - Knowledge Retrieval System

This directory contains two independent deployment options for the Knowledge Retrieval System:

## 🏗️ Deployment Options

### 1. CDK Deployment (`infrastructure/cdk/`)
- **Best for**: Development environments, teams familiar with CDK
- **Requirements**: Node.js, AWS CDK, CDK Bootstrap
- **Benefits**: Type-safe infrastructure, easier development iteration

### 2. Pure CloudFormation Deployment (`infrastructure/cfn/`)
- **Best for**: Production environments, CI/CD pipelines, enterprise deployments
- **Requirements**: Only AWS CLI and Python (no CDK dependencies)
- **Benefits**: No CDK bootstrap required, standard CloudFormation tooling

## 📁 Directory Structure

```
infrastructure/
├── README.md                    # This file
├── cdk/                        # CDK deployment option
│   ├── bin/
│   │   └── app.py              # CDK app entry point
│   ├── lib/
│   │   ├── knowledge_retrieval_stack.py  # Main CDK stack
│   │   └── stacks/             # Individual CDK stacks
│   │       ├── network_stack.py
│   │       ├── storage_stack.py
│   │       ├── compute_stack.py
│   │       └── monitoring_stack.py
│   ├── env/                    # CDK environment configurations
│   │   ├── .env.dev
│   │   └── .env.prod
│   └── README.md               # CDK-specific documentation
└── cfn/                        # Pure CloudFormation deployment option
    ├── templates/              # Hand-written CloudFormation templates
    │   ├── knowledge-retrieval-main.yaml
    │   ├── network-stack.yaml
    │   ├── storage-stack.yaml
    │   ├── compute-stack.yaml
    │   └── monitoring-stack.yaml
    ├── parameters/             # Environment-specific parameters
    │   ├── dev.json
    │   └── prod.json
    ├── scripts/
    │   └── deploy.py           # Pure CloudFormation deployment script
    └── README.md               # CloudFormation-specific documentation
```

## 🚀 Quick Start

### CDK Deployment
```bash
# Set environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************

# Deploy with CDK (requires CDK bootstrap)
make deploy ENV=dev
```

### Pure CloudFormation Deployment
```bash
# Set environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************

# Deploy with pure CloudFormation (no CDK required)
make cfn-deploy ENV=dev
```

## 🔄 Migration Between Methods

Both deployment methods create **identical infrastructure**. You can migrate between them:

### From CDK to CloudFormation
1. Export CDK stack outputs: `make outputs ENV=prod`
2. Deploy CloudFormation with same configuration: `make cfn-deploy ENV=prod`
3. Verify functionality matches
4. Optionally destroy CDK stack: `make destroy ENV=prod`

### From CloudFormation to CDK
1. Export CloudFormation outputs: `make cfn-outputs ENV=prod`
2. Deploy CDK with same configuration: `make deploy ENV=prod`
3. Verify functionality matches
4. Optionally destroy CloudFormation stack: `make cfn-destroy ENV=prod`

## 📋 Available Commands

### CDK Commands
| Command | Description |
|---------|-------------|
| `make bootstrap` | Bootstrap CDK (one-time setup) |
| `make deploy ENV=dev` | Deploy with CDK |
| `make synth ENV=dev` | Synthesize CDK templates |
| `make diff ENV=dev` | Show CDK differences |
| `make destroy ENV=dev` | Destroy CDK stack |

### CloudFormation Commands
| Command | Description |
|---------|-------------|
| `make cfn-validate` | Validate CloudFormation templates |
| `make cfn-deploy ENV=dev` | Deploy with pure CloudFormation |
| `make cfn-update ENV=dev` | Update CloudFormation stack |
| `make cfn-destroy ENV=dev` | Destroy CloudFormation stack |
| `make cfn-status ENV=dev` | Show CloudFormation stack status |
| `make cfn-outputs ENV=dev` | Display stack outputs |

### Universal Commands
| Command | Description |
|---------|-------------|
| `make all ENV=dev` | Complete CDK deployment |
| `make all-cfn ENV=dev` | Complete CloudFormation deployment |
| `make build` | Build Lambda functions and layers |
| `make test` | Run tests |
| `make clean` | Clean build artifacts |

## ⚙️ Configuration

### Environment Variables
Both deployment methods use the same environment variables:

**Required:**
```bash
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************
```

**Optional:**
```bash
export AWS_REGION=us-east-1
export LAMBDA_MEMORY_SIZE=512
export LAMBDA_TIMEOUT=30
export OPENSEARCH_INSTANCE_TYPE=t3.small.search
export AMAZON_Q_APPLICATION_ID=your-amazon-q-app-id
```

**For Existing VPC:**
```bash
export VPC_ID=vpc-********
export PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890
export PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij
export EXISTING_LAMBDA_SG_ID=sg-lambda123
export EXISTING_OPENSEARCH_SG_ID=sg-opensearch456
```

## 🏢 Enterprise Considerations

### When to Use CDK
- **Development environments** where iteration speed matters
- **Teams familiar with CDK** and infrastructure as code
- **Environments where CDK bootstrap is acceptable**

### When to Use Pure CloudFormation
- **Production environments** requiring standard AWS tooling
- **CI/CD pipelines** that need CloudFormation-native support
- **Enterprise environments** with restrictions on CDK dependencies
- **Compliance requirements** needing explicit CloudFormation templates
- **Environments where CDK bootstrap is not allowed**

## 🔒 Security

Both deployment methods implement identical security:
- **VPC isolation** with private subnets for Lambda functions
- **Security groups** with minimal required access
- **IAM roles** following least privilege principle
- **Encryption at rest** for all data stores
- **HTTPS/TLS** for all communications

## 📊 Monitoring

Both deployments include:
- **CloudWatch Dashboard** with system metrics
- **CloudWatch Alarms** for error rates and latency
- **Lambda function monitoring** with error tracking
- **API Gateway monitoring** with request/response metrics
- **SNS notifications** for alarm events

## 🛠️ Troubleshooting

### CDK Issues
```bash
# CDK not bootstrapped
make bootstrap

# CDK dependencies missing
cd infrastructure/cdk && npm install

# Environment configuration issues
make setup-env-cdk ENV=dev
```

### CloudFormation Issues
```bash
# Template validation
make cfn-validate

# Stack status check
make cfn-status ENV=dev

# View stack events
aws cloudformation describe-stack-events --stack-name your-stack-name
```

## 📚 Documentation

- **CDK Documentation**: See `infrastructure/cdk/README.md`
- **CloudFormation Documentation**: See `infrastructure/cfn/README.md`
- **Deployment Guide**: See `DEPLOYMENT_GUIDE.md` in project root
- **Architecture**: See `UPDATED_ARCHITECTURE_DIAGRAM.md` in project root

## 🤝 Contributing

When making infrastructure changes:
1. **Update both CDK and CloudFormation** templates to maintain parity
2. **Test both deployment methods** in development environment
3. **Update documentation** for any new parameters or resources
4. **Validate templates** before committing changes

## 📞 Support

For infrastructure-related issues:
1. Check the troubleshooting section above
2. Review CloudWatch logs and metrics
3. Consult the deployment-specific README files
4. Check AWS CloudFormation/CDK documentation
