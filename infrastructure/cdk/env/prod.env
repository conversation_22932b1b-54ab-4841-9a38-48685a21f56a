CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=us-east-1
ENV=prod
VPC_ID=vpc-yyyyyyyyyyyyyyy

# Lambda settings
LAMBDA_MEMORY_SIZE=1024
LAMBDA_TIMEOUT=60

# DynamoDB settings
DYNAMODB_BILLING_MODE=PROVISIONED
DYNAMODB_READ_CAPACITY=10
DYNAMODB_WRITE_CAPACITY=10
DYNAMODB_TTL_DAYS=30

# OpenSearch settings
OPENSEARCH_INSTANCE_TYPE=m5.large.search
OPENSEARCH_INSTANCE_COUNT=2

# SQS settings
SQS_RETENTION_PERIOD=86400  # 24 hours
SQS_VISIBILITY_TIMEOUT=120

# Bedrock settings
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0
EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1

# Slack settings
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here 