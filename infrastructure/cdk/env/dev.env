CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=ap-southeast-2
ENV=dev
VPC_ID=vpc-xxxxxxxxxxxxxxx

# Lambda settings
LAMBDA_MEMORY_SIZE=512
LAMBDA_TIMEOUT=30

# DynamoDB settings
DYNAMODB_BILLING_MODE=PAY_PER_REQUEST
DYNAMODB_TTL_DAYS=7

# OpenSearch settings
OPENSEARCH_INSTANCE_TYPE=t3.small.search
OPENSEARCH_INSTANCE_COUNT=1

# SQS settings
SQS_RETENTION_PERIOD=14400  # 4 hours
SQS_VISIBILITY_TIMEOUT=60

# Bedrock settings
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0
EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1

# Slack settings
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here 