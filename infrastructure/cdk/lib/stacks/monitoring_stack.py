from typing import List

from aws_cdk import NestedStack
from aws_cdk import aws_apigateway as apigateway
from aws_cdk import aws_cloudwatch as cloudwatch
from aws_cdk import aws_cloudwatch_actions as cloudwatch_actions
from aws_cdk import aws_lambda as lambda_
from aws_cdk import aws_sns as sns
from constructs import Construct


class MonitoringStack(NestedStack):
    """Stack for monitoring resources like CloudWatch alarms and dashboards."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        lambda_functions: List[lambda_.IFunction],
        api_gateway: apigateway.IRestApi,
        **kwargs,
    ):
        super().__init__(scope, id, **kwargs)

        # Constants for comparison operators
        GTE = cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD
        GT = cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD

        # Create SNS Topic for alarm notifications
        alarm_topic = sns.Topic(
            self,
            "AlarmTopic",
            topic_name=f"knowledge-retrieval-alarms-{env_name}",
        )

        # Create dashboard
        dashboard = cloudwatch.Dashboard(
            self,
            "KnowledgeRetrievalDashboard",
            dashboard_name=f"KnowledgeRetrieval-{env_name}",
        )

        # Add Lambda metrics to dashboard
        lambda_widgets = []
        for function in lambda_functions:
            # Create error alarm for each Lambda
            error_alarm = cloudwatch.Alarm(
                self,
                f"{function.function_name}ErrorsAlarm",
                alarm_name=f"{function.function_name}-errors",
                metric=function.metric_errors(),
                threshold=1,
                evaluation_periods=1,
                comparison_operator=GTE,
                treat_missing_data=cloudwatch.TreatMissingData.NOT_BREACHING,
            )
            error_alarm.add_alarm_action(
                cloudwatch_actions.SnsAction(alarm_topic)
            )

            # Duration alarm
            duration_alarm = cloudwatch.Alarm(
                self,
                f"{function.function_name}DurationAlarm",
                alarm_name=f"{function.function_name}-duration",
                metric=function.metric_duration(),
                # 80% of timeout
                threshold=function.timeout.to_seconds() * 0.8,
                evaluation_periods=3,
                datapoints_to_alarm=2,
                comparison_operator=GT,
                treat_missing_data=cloudwatch.TreatMissingData.NOT_BREACHING,
            )
            duration_alarm.add_alarm_action(
                cloudwatch_actions.SnsAction(alarm_topic)
            )

            # Add widgets for this Lambda to dashboard
            lambda_widgets.append(
                cloudwatch.GraphWidget(
                    title=(f"{function.function_name} Invocations and Errors"),
                    left=[
                        function.metric_invocations(),
                        function.metric_errors(),
                    ],
                    width=12,
                )
            )
            lambda_widgets.append(
                cloudwatch.GraphWidget(
                    title=f"{function.function_name} Duration",
                    left=[function.metric_duration()],
                    width=12,
                )
            )

        # API Gateway metrics - 5xx errors alarm
        api_5xx_alarm = cloudwatch.Alarm(
            self,
            "APIGateway5xxAlarm",
            alarm_name=f"APIGateway-5xx-{env_name}",
            metric=cloudwatch.Metric(
                namespace="AWS/ApiGateway",
                metric_name="5XXError",
                dimensions_map={"ApiName": api_gateway.rest_api_name},
                statistic="Sum",
            ),
            threshold=5,
            evaluation_periods=3,
            datapoints_to_alarm=2,
            comparison_operator=GT,
            treat_missing_data=cloudwatch.TreatMissingData.NOT_BREACHING,
        )
        api_5xx_alarm.add_alarm_action(
            cloudwatch_actions.SnsAction(alarm_topic)
        )

        # API Gateway metrics - 4xx errors alarm
        api_4xx_alarm = cloudwatch.Alarm(
            self,
            "APIGateway4xxAlarm",
            alarm_name=f"APIGateway-4xx-{env_name}",
            metric=cloudwatch.Metric(
                namespace="AWS/ApiGateway",
                metric_name="4XXError",
                dimensions_map={"ApiName": api_gateway.rest_api_name},
                statistic="Sum",
            ),
            threshold=20,
            evaluation_periods=3,
            datapoints_to_alarm=2,
            comparison_operator=GT,
            treat_missing_data=cloudwatch.TreatMissingData.NOT_BREACHING,
        )
        api_4xx_alarm.add_alarm_action(
            cloudwatch_actions.SnsAction(alarm_topic)
        )

        # Add API Gateway metrics to dashboard
        api_widgets = [
            cloudwatch.GraphWidget(
                title="API Gateway HTTP Status Codes",
                left=[
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="Count",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Sum",
                        label="Total Requests",
                    ),
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="4XXError",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Sum",
                        label="4XX Errors",
                    ),
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="5XXError",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Sum",
                        label="5XX Errors",
                    ),
                ],
                width=24,
            ),
            cloudwatch.GraphWidget(
                title="API Gateway Latency",
                left=[
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="Latency",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Average",
                        label="Average Latency",
                    ),
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="IntegrationLatency",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Average",
                        label="Integration Latency",
                    ),
                ],
                width=12,
            ),
            cloudwatch.GraphWidget(
                title="API Gateway Cache Performance",
                left=[
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="CacheHitCount",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Sum",
                        label="Cache Hits",
                    ),
                    cloudwatch.Metric(
                        namespace="AWS/ApiGateway",
                        metric_name="CacheMissCount",
                        dimensions_map={"ApiName": api_gateway.rest_api_name},
                        statistic="Sum",
                        label="Cache Misses",
                    ),
                ],
                width=12,
            ),
        ]

        # Add all widgets to dashboard
        dashboard.add_widgets(
            cloudwatch.TextWidget(
                markdown=f"# Knowledge Retrieval System - {env_name.upper()}",
                width=24,
                height=1,
            ),
            *lambda_widgets,
            cloudwatch.TextWidget(
                markdown="## API Gateway Metrics", width=24, height=1
            ),
            *api_widgets,
        )
