import os
from typing import List, Optional
from aws_cdk import NestedStack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct


class NetworkStack(NestedStack):
    """Stack for networking resources like VPC and subnets."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        vpc_id: str = None,
        existing_lambda_sg_id: str = None,
        existing_opensearch_sg_id: str = None,
        **kwargs,
    ):
        super().__init__(scope, id, **kwargs)

        # Store configuration
        self.env_name = env_name
        self.existing_lambda_sg_id = existing_lambda_sg_id
        self.existing_opensearch_sg_id = existing_opensearch_sg_id

        # Get existing VPC or create a new one
        if vpc_id:
            self.vpc = ec2.Vpc.from_lookup(self, "ExistingVPC", vpc_id=vpc_id)
        else:
            # Create a new VPC if vpc_id is not provided
            self.vpc = ec2.Vpc(
                self,
                "KnowledgeRetrievalVPC",
                vpc_name=f"knowledge-retrieval-vpc-{env_name}",
                max_azs=2,
                nat_gateways=1,
                subnet_configuration=[
                    ec2.SubnetConfiguration(
                        name="Public",
                        subnet_type=ec2.SubnetType.PUBLIC,
                        cidr_mask=24,
                    ),
                    ec2.SubnetConfiguration(
                        name="Private",
                        subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                        cidr_mask=24,
                    ),
                ],
            )

        # Create or import security groups
        self._setup_security_groups()

    def _setup_security_groups(self):
        """Create or import security groups based on configuration."""

        # Lambda Security Group
        if self.existing_lambda_sg_id:
            self.lambda_security_group = ec2.SecurityGroup.from_security_group_id(
                self, "ExistingLambdaSecurityGroup", self.existing_lambda_sg_id
            )
            print(f"Using existing Lambda security group: {self.existing_lambda_sg_id}")
        else:
            self.lambda_security_group = ec2.SecurityGroup(
                self,
                "LambdaSecurityGroup",
                vpc=self.vpc,
                description=f"Security group for Lambda functions - {self.env_name}",
                security_group_name=f"lambda-sg-{self.env_name}",
                allow_all_outbound=False,
            )

            # Add HTTPS outbound for AWS services (443)
            self.lambda_security_group.add_egress_rule(
                peer=ec2.Peer.any_ipv4(),
                connection=ec2.Port.tcp(443),
                description="HTTPS outbound for AWS services (Bedrock, DynamoDB, S3, SQS)"
            )

        # OpenSearch Security Group
        if self.existing_opensearch_sg_id:
            self.opensearch_security_group = ec2.SecurityGroup.from_security_group_id(
                self, "ExistingOpenSearchSecurityGroup", self.existing_opensearch_sg_id
            )
            print(f"Using existing OpenSearch security group: {self.existing_opensearch_sg_id}")
        else:
            self.opensearch_security_group = ec2.SecurityGroup(
                self,
                "OpenSearchSecurityGroup",
                vpc=self.vpc,
                description=f"Security group for OpenSearch cluster - {self.env_name}",
                security_group_name=f"opensearch-sg-{self.env_name}",
                allow_all_outbound=False,
            )

            # Allow Lambda to access OpenSearch on port 443
            self.opensearch_security_group.add_ingress_rule(
                peer=ec2.Peer.security_group_id(self.lambda_security_group.security_group_id),
                connection=ec2.Port.tcp(443),
                description="Allow Lambda functions to access OpenSearch"
            )




