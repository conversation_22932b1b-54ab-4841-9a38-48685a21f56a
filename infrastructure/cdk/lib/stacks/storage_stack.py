import os

from aws_cdk import Duration, NestedStack, RemovalP<PERSON><PERSON>
from aws_cdk import aws_dynamodb as dynamodb
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_iam as iam
from aws_cdk import aws_opensearchservice as opensearch
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_sqs as sqs
from constructs import Construct


class StorageStack(NestedStack):
    """Stack for storage resources like DynamoDB, S3, SQS, and OpenSearch."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        vpc: ec2.IVpc,
        lambda_security_group: ec2.ISecurityGroup = None,
        opensearch_security_group: ec2.ISecurityGroup = None,
        **kwargs
    ):
        super().__init__(scope, id, **kwargs)

        # Environment variables
        dynamodb_billing_mode = os.environ.get(
            "DYNAMODB_BILLING_MODE", "PAY_PER_REQUEST"
        )
        opensearch_instance_type = os.environ.get(
            "OPENSEARCH_INSTANCE_TYPE", "t3.small.search"
        )
        opensearch_instance_count = int(
            os.environ.get("OPENSEARCH_INSTANCE_COUNT", "1")
        )
        sqs_retention_period = int(
            os.environ.get("SQS_RETENTION_PERIOD", "14400")
        )  # 4 hours
        sqs_visibility_timeout = int(
            os.environ.get("SQS_VISIBILITY_TIMEOUT", "60")
        )

        # S3 Bucket for document storage
        self.document_bucket = s3.Bucket(
            self,
            "DocumentBucket",
            bucket_name=f"knowledge-retrieval-documents-{env_name}-{self.account}",
            removal_policy=RemovalPolicy.RETAIN,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            versioned=True,
        )

        # DynamoDB Table for conversation history
        self.conversation_table = dynamodb.Table(
            self,
            "ConversationHistoryTable",
            table_name=f"knowledge-retrieval-conversations-{env_name}",
            partition_key=dynamodb.Attribute(
                name="conversation_id", type=dynamodb.AttributeType.STRING
            ),
            sort_key=dynamodb.Attribute(
                name="timestamp", type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode[dynamodb_billing_mode],
            removal_policy=RemovalPolicy.RETAIN,
            time_to_live_attribute="ttl",
        )

        # Provision capacity if in PROVISIONED mode
        if dynamodb_billing_mode == "PROVISIONED":
            read_capacity = int(os.environ.get("DYNAMODB_READ_CAPACITY", "5"))
            write_capacity = int(
                os.environ.get("DYNAMODB_WRITE_CAPACITY", "5")
            )
            self.conversation_table.apply_removal_policy(RemovalPolicy.RETAIN)
            self.conversation_table.auto_scale_read_capacity(
                min_capacity=read_capacity, max_capacity=read_capacity * 5
            ).scale_on_utilization(target_utilization_percent=70)
            self.conversation_table.auto_scale_write_capacity(
                min_capacity=write_capacity, max_capacity=write_capacity * 5
            ).scale_on_utilization(target_utilization_percent=70)

        # OpenSearch Domain for vector storage - VPC enabled for security
        opensearch_config = {
            "domain_name": f"knowledge-retrieval-{env_name}",
            "version": opensearch.EngineVersion.OPENSEARCH_2_5,
            "capacity": opensearch.CapacityConfig(
                data_node_instance_type=opensearch_instance_type,
                data_nodes=opensearch_instance_count,
            ),
            "ebs": opensearch.EbsOptions(
                enabled=True,
                volume_size=100,
                volume_type=ec2.EbsDeviceVolumeType.GP3,
            ),
            "removal_policy": RemovalPolicy.RETAIN,
            "encryption_at_rest": opensearch.EncryptionAtRestOptions(enabled=True),
            "node_to_node_encryption": True,
            "enforce_https": True,
        }

        # Configure VPC access if security group is provided
        if opensearch_security_group:
            opensearch_config["vpc"] = opensearch.VpcOptions(
                subnets=vpc.select_subnets(
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
                ).subnets,
                security_groups=[opensearch_security_group]
            )
            # For VPC domains, use fine-grained access control instead of resource-based policies
            opensearch_config["fine_grained_access_control"] = opensearch.AdvancedSecurityOptions(
                master_user_arn=f"arn:aws:iam::{self.account}:root"
            )
        else:
            # Fallback to resource-based policy for non-VPC domains
            opensearch_config["access_policies"] = [
                iam.PolicyStatement(
                    actions=["es:*"],
                    effect=iam.Effect.ALLOW,
                    resources=["*"],
                    principals=[iam.AnyPrincipal()],
                )
            ]

        self.opensearch_domain = opensearch.Domain(
            self,
            "OpenSearchDomain",
            **opensearch_config
        )

        # SQS FIFO Queue for message processing
        self.message_queue = sqs.Queue(
            self,
            "MessageQueue",
            queue_name=f"knowledge-retrieval-messages-{env_name}.fifo",
            visibility_timeout=Duration.seconds(sqs_visibility_timeout),
            retention_period=Duration.seconds(sqs_retention_period),
            fifo=True,
            content_based_deduplication=True,
            removal_policy=RemovalPolicy.DESTROY,
        )
