#!/usr/bin/env python3
"""AWS CDK application entry point."""

import os

import dotenv
from aws_cdk import App, Environment, Tags
from lib.knowledge_retrieval_stack import KnowledgeRetrievalStack

# Load environment variables from .env file
dotenv.load_dotenv()

# Get environment name from environment variable
env_name = os.getenv("ENV_NAME", "dev")
aws_account = os.getenv("CDK_DEFAULT_ACCOUNT")
aws_region = os.getenv("CDK_DEFAULT_REGION", "ap-southeast-2")

# Create CDK app
app = App()

# Environment configuration
env_config = (
    Environment(account=aws_account, region=aws_region)
    if aws_account
    else None
)

# Create the main stack
stack = KnowledgeRetrievalStack(
    app,
    f"KnowledgeRetrievalStack-{env_name}",
    env_name=env_name,
    env=env_config,
)

# Add common tags
Tags.of(app).add("Environment", env_name)
Tags.of(app).add("Project", "IntelligentKnowledgeRetrieval")

# Synthesize the app
app.synth()
