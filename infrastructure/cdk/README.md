# Intelligent Knowledge Retrieval System - CDK Infrastructure

This directory contains the AWS CDK infrastructure code for the Intelligent Knowledge Retrieval System.

## Project Structure

The infrastructure is organized into modular stacks to improve readability and maintainability:

```
infrastructure/
├── bin/
│   └── app.py               # Main CDK app entry point
├── env/
│   ├── dev.env              # Development environment configuration
│   └── prod.env             # Production environment configuration
├── lib/
│   ├── knowledge_retrieval_stack.py   # Main parent stack
│   └── stacks/              # Individual modular stacks
│       ├── __init__.py
│       ├── network_stack.py           # VPC and networking resources
│       ├── storage_stack.py           # DynamoDB, S3, SQS, OpenSearch
│       ├── compute_stack.py           # Lambda functions and API Gateway
│       └── monitoring_stack.py        # CloudWatch alarms and dashboards
└── README.md                # This file
```

## Lambda Functions

The system includes several Lambda functions:

1. **Reader Lambda**: Container-based Lambda that processes incoming Slack messages
   - Deployed from a Docker image built by CDK and stored in ECR
   - Uses Slack Bolt framework to handle Slack events
   - Source code in `lambda/reader/src/`

2. **Writer Lambda**: Processes messages from SQS and generates responses
   - Deployed as a regular Lambda function
   - Source code in `lambda/writer/`

3. **Ingestion Lambda**: Processes new documents and indexes them
   - Deployed as a regular Lambda function
   - Source code in `lambda/ingestion/`

## Stack Organization

Individual stacks for better separation of concerns:

- **Network Stack**: VPC, subnets, security groups, and networking resources
- **Storage Stack**: S3 buckets, DynamoDB tables, OpenSearch domain
- **Compute Stack**: Lambda functions and API Gateway
- **Monitoring Stack**: CloudWatch dashboards, alarms, and logs

### Stack Files:
```
lib/stacks/
├── network_stack.py              # VPC and networking
├── storage_stack.py              # DynamoDB, S3, OpenSearch
├── compute_stack.py              # Lambda functions and API Gateway
└── monitoring_stack.py           # CloudWatch resources
```

## Key AWS Services Used

- **AWS Lambda**: Serverless compute for event processing
- **API Gateway**: RESTful API for Slack integration
- **Amazon DynamoDB**: Conversation history storage
- **Amazon OpenSearch**: Vector search and document indexing
- **Amazon S3**: Document storage and static assets
- **Amazon SQS**: Message queuing for reliable processing
- **Amazon Bedrock**: AI/ML inference (Claude 3)
- **AWS CloudWatch**: Monitoring, logging, and alerting

## Stack Architecture

The infrastructure is divided into the following stacks:

1. **NetworkStack**: Manages VPC, subnets, and networking resources
2. **StorageStack**: Manages data storage resources:
   - DynamoDB table for conversation history
   - S3 bucket for document storage
   - SQS FIFO queue for message processing
   - OpenSearch domain for vector storage
3. **ComputeStack**: Manages compute resources:
   - Lambda functions (Reader, Writer, Ingestion)
   - Lambda layers
   - API Gateway
   - IAM roles and permissions
   - ECR repository for container images
4. **MonitoringStack**: Manages monitoring resources:
   - CloudWatch alarms
   - CloudWatch dashboard
   - SNS topic for notifications

The `KnowledgeRetrievalStack` is a parent stack that composes all these individual stacks together.

## Deployment

To deploy the infrastructure:

1. Configure the environment variables in `env/dev.env` or `env/prod.env`
2. Run the following commands:

```bash
# For development environment
cdk deploy --context env=dev

# For production environment
cdk deploy --context env=prod
```

## Environment Variables

The infrastructure uses environment variables defined in the `.env` files to configure resources. Key variables include:

- `CDK_DEFAULT_ACCOUNT`: AWS account ID
- `CDK_DEFAULT_REGION`: AWS region
- `VPC_ID`: Existing VPC ID (optional)
- `LAMBDA_MEMORY_SIZE`: Memory size for Lambda functions
- `LAMBDA_TIMEOUT`: Timeout for Lambda functions
- `DYNAMODB_BILLING_MODE`: DynamoDB billing mode (PAY_PER_REQUEST or PROVISIONED)
- `OPENSEARCH_INSTANCE_TYPE`: OpenSearch instance type
- `BEDROCK_MODEL_ID`: Amazon Bedrock model ID for LLM
- `EMBEDDING_MODEL_ID`: Model ID for embeddings
- `SLACK_BOT_TOKEN`: Slack Bot token for Reader Lambda
- `SLACK_SIGNING_SECRET`: Slack signing secret for Reader Lambda 