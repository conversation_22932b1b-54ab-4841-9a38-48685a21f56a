"""
Reader Lambda - Slack Integration and Message Processing.

This module handles Slack events, processes user queries, and forwards them
to the SQS queue for further processing by the Writer Lambda.
"""

import json
import os

import sys
import time
from typing import Any, Dict

import boto3
from botocore.exceptions import ClientError
from slack_bolt import App
from slack_bolt.adapter.aws_lambda import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from exceptions import (
    AuthenticationException,
    ConfigurationException,
    MessageValidationException,
    SlackAPIException,
    SlackEventException,
    SQSIntegrationException,
)
from logger_config import (
    create_slack_correlation_context,
    log_execution_time,
    log_with_context,
    setup_logger,
)

# Local imports
from slack_models import SlackEvent, SlackMessage, SlackCommand, SQSMessagePayload

# Configure structured logger
logger = setup_logger(__name__)

# Environment variables with validation
REQUIRED_ENV_VARS = ["SLACK_BOT_TOKEN", "SLACK_SIGNING_SECRET", "QUEUE_URL"]

for var in REQUIRED_ENV_VARS:
    if not os.environ.get(var):
        raise ConfigurationException(
            f"Required environment variable {var} is missing"
        )

SLACK_BOT_TOKEN = os.environ.get("SLACK_BOT_TOKEN")
SLACK_SIGNING_SECRET = os.environ.get("SLACK_SIGNING_SECRET")
QUEUE_URL = os.environ.get("QUEUE_URL")

# Initialize AWS clients
try:
    sqs = boto3.client("sqs")
except Exception as e:
    raise ConfigurationException(f"Failed to initialize SQS client: {str(e)}")

# Initialize Slack Bolt app
try:
    app = App(
        token=SLACK_BOT_TOKEN,
        signing_secret=SLACK_SIGNING_SECRET,
        process_before_response=True,
    )
except Exception as e:
    raise AuthenticationException(f"Failed to initialize Slack app: {str(e)}")


@log_execution_time(logger)
def validate_slack_event(event: Dict[str, Any]) -> SlackEvent:
    """Validate and parse incoming Slack event with error handling."""
    try:
        required_fields = ["user", "channel", "text", "ts"]
        for field in required_fields:
            if field not in event:
                raise MessageValidationException(
                    f"Missing required field: {field}",
                    context={"event": event},
                )

        slack_event = SlackEvent(
            event_type=event.get("type", "message"),
            user_id=event["user"],
            channel_id=event["channel"],
            text=event["text"],
            timestamp=event["ts"],
            thread_ts=event.get("thread_ts"),
            bot_id=event.get("bot_id"),
        )

        if slack_event.is_from_bot:
            raise MessageValidationException(
                "Ignoring bot message", context={"bot_id": slack_event.bot_id}
            )

        log_with_context(
            logger,
            "debug",
            "Successfully validated Slack event",
            user_id=slack_event.user_id,
            channel_id=slack_event.channel_id,
            event_type=slack_event.event_type,
        )

        return slack_event

    except MessageValidationException:
        raise
    except Exception as e:
        raise SlackEventException(
            f"Failed to validate Slack event: {str(e)}",
            context={"event": event},
        )


@log_execution_time(logger)
def create_message_deduplication_id(conversation_id: str, query: str) -> str:
    """Create a unique deduplication ID for SQS FIFO."""
    try:
        content = f"{conversation_id}-{query}"
        # SQS deduplication ID has a 128 character limit
        if len(content) <= 128:
            return content
        else:
            return content[:125] + "..."
    except Exception as e:
        raise SQSIntegrationException(
            f"Failed to create deduplication ID: {str(e)}",
            context={"conversation_id": conversation_id, "query": query},
        )


@log_execution_time(logger)
def send_to_sqs(slack_message: SlackMessage) -> bool:
    """Send message to SQS queue with comprehensive error handling."""
    try:
        correlation_ctx = create_slack_correlation_context(
            user_id=slack_message.user_id,
            channel_id=slack_message.channel_id,
            conversation_id=slack_message.conversation_id,
        )

        log_with_context(
            logger,
            "info",
            "Sending message to SQS",
            correlation_ctx,
            query_length=len(slack_message.query),
        )

        message_dict = slack_message.to_dict()
        dedup_id = create_message_deduplication_id(
            slack_message.conversation_id, slack_message.query
        )

        sqs_payload = SQSMessagePayload(
            message_body=message_dict,
            message_group_id=slack_message.conversation_id,
            deduplication_id=dedup_id,
            queue_url=QUEUE_URL,
        )

        response = sqs.send_message(
            QueueUrl=sqs_payload.queue_url,
            MessageBody=json.dumps(sqs_payload.message_body),
            MessageGroupId=sqs_payload.message_group_id,
            MessageDeduplicationId=sqs_payload.deduplication_id,
        )

        log_with_context(
            logger,
            "info",
            "Successfully sent message to SQS",
            correlation_ctx,
            message_id=response["MessageId"],
            dedup_id=dedup_id,
        )

        return True

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise SQSIntegrationException(
            f"AWS SQS error: {str(e)}",
            error_code=error_code,
            context={
                "conversation_id": slack_message.conversation_id,
                "queue_url": QUEUE_URL,
            },
        )
    except Exception as e:
        raise SQSIntegrationException(
            f"Unexpected error sending to SQS: {str(e)}",
            context={
                "conversation_id": slack_message.conversation_id,
                "queue_url": QUEUE_URL,
            },
        )


@log_execution_time(logger)
def process_slack_message(
    slack_event: SlackEvent, thread_ts: str = None
) -> SlackMessage:
    """Process and create SlackMessage from validated event."""
    try:
        # Remove bot mention from text if present
        query = slack_event.text
        if query.startswith("<@") and ">" in query:
            query = query.split(">", 1)[1].strip()

        if not query.strip():
            raise MessageValidationException(
                "Empty query after processing",
                context={"original_text": slack_event.text},
            )

        conversation_id = (
            f"{slack_event.channel_id}-{thread_ts or slack_event.timestamp}"
        )

        slack_message = SlackMessage(
            query=query,
            user_id=slack_event.user_id,
            channel_id=slack_event.channel_id,
            thread_ts=thread_ts or slack_event.thread_ts,
            conversation_id=conversation_id,
            timestamp=str(int(time.time())),
        )

        log_with_context(
            logger,
            "info",
            "Successfully processed Slack message",
            user_id=slack_message.user_id,
            channel_id=slack_message.channel_id,
            conversation_id=conversation_id,
            query_length=len(query),
        )

        return slack_message

    except MessageValidationException:
        raise
    except Exception as e:
        raise SlackEventException(
            f"Failed to process Slack message: {str(e)}",
            context={"event": slack_event.to_dict()},
        )


@log_execution_time(logger)
def parse_slash_command(command_data: Dict[str, Any]) -> SlackCommand:
    """Parse incoming slash command data."""
    try:
        slack_command = SlackCommand(
            command=command_data.get("command", ""),
            text=command_data.get("text", ""),
            user_id=command_data.get("user_id", ""),
            user_name=command_data.get("user_name", ""),
            channel_id=command_data.get("channel_id", ""),
            channel_name=command_data.get("channel_name", ""),
            team_id=command_data.get("team_id", ""),
            team_domain=command_data.get("team_domain", ""),
            response_url=command_data.get("response_url", ""),
            trigger_id=command_data.get("trigger_id", ""),
        )

        log_with_context(
            logger,
            "debug",
            "Successfully parsed slash command",
            user_id=slack_command.user_id,
            command=slack_command.command,
            text_length=len(slack_command.text),
        )

        return slack_command

    except Exception as e:
        raise SlackEventException(
            f"Failed to parse slash command: {str(e)}",
            context={"command_data": command_data},
        )


@log_execution_time(logger)
def process_slash_command(slack_command: SlackCommand) -> SlackMessage:
    """Process slash command and create SlackMessage."""
    try:
        if not slack_command.text.strip():
            raise MessageValidationException(
                "Empty question in slash command",
                context={"command": slack_command.command},
            )

        slack_message = SlackMessage(
            query=slack_command.text.strip(),
            user_id=slack_command.user_id,
            channel_id=slack_command.channel_id,
            thread_ts=None,  # Slash commands don't use threads
            conversation_id=slack_command.conversation_id,
            timestamp=str(int(time.time())),
        )

        # Add response_url to the message for later use
        slack_message_dict = slack_message.to_dict()
        slack_message_dict["response_url"] = slack_command.response_url
        slack_message_dict["is_slash_command"] = True

        log_with_context(
            logger,
            "info",
            "Successfully processed slash command",
            user_id=slack_message.user_id,
            conversation_id=slack_message.conversation_id,
            query_length=len(slack_message.query),
        )

        return slack_message, slack_message_dict

    except MessageValidationException:
        raise
    except Exception as e:
        raise SlackEventException(
            f"Failed to process slash command: {str(e)}",
            context={"command": slack_command.to_dict()},
        )


@log_execution_time(logger)
def send_slack_response(say, message: str, thread_ts: str = None) -> bool:
    """Send response back to Slack with error handling."""
    try:
        say({"text": message, "thread_ts": thread_ts})

        log_with_context(
            logger,
            "debug",
            "Successfully sent Slack response",
            thread_ts=thread_ts,
            response_length=len(message),
        )

        return True

    except Exception as e:
        raise SlackAPIException(
            f"Failed to send Slack response: {str(e)}",
            context={"message": message, "thread_ts": thread_ts},
        )


@app.event("app_mention")
def handle_app_mention(event: Dict[str, Any], say) -> None:
    """Handle when the bot is mentioned in a channel."""
    correlation_ctx = None

    try:
        # Validate and parse event
        slack_event = validate_slack_event(event)

        correlation_ctx = create_slack_correlation_context(
            user_id=slack_event.user_id,
            channel_id=slack_event.channel_id,
            event_type="app_mention",
        )

        log_with_context(
            logger,
            "info",
            "Processing app mention",
            correlation_ctx,
            text_length=len(slack_event.text),
        )

        # Process the message
        thread_ts = slack_event.thread_ts or slack_event.timestamp
        slack_message = process_slack_message(slack_event, thread_ts)

        # Send acknowledgment
        send_slack_response(
            say,
            "I'm processing your question. I'll respond shortly...",
            thread_ts,
        )

        # Send to SQS
        if not send_to_sqs(slack_message):
            send_slack_response(
                say,
                "Sorry, I encountered an error processing your request. "
                "Please try again later.",
                thread_ts,
            )

        log_with_context(
            logger, "info", "Successfully handled app mention", correlation_ctx
        )

    except MessageValidationException as e:
        log_with_context(
            logger,
            "warning",
            "Invalid message in app mention",
            correlation_ctx,
            error=str(e),
        )
        # Don't send error response for validation issues

    except (
        SlackEventException,
        SQSIntegrationException,
        SlackAPIException,
    ) as e:
        log_with_context(
            logger,
            "error",
            "Error handling app mention",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )
        try:
            send_slack_response(
                say,
                "Sorry, something went wrong. Please try again later.",
                event.get("ts"),
            )
        except SlackAPIException:
            pass  # Avoid infinite error loops

    except Exception as e:
        log_with_context(
            logger,
            "error",
            "Unexpected error in app mention handler",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )
        try:
            send_slack_response(
                say,
                "Sorry, something went wrong. Please try again later.",
                event.get("ts"),
            )
        except Exception:
            pass  # Avoid infinite error loops


@app.event("message")
def handle_message(event: Dict[str, Any], say) -> None:
    """Handle direct messages to the bot."""
    correlation_ctx = None

    try:
        # Skip bot messages
        if event.get("bot_id"):
            return

        # Process only direct messages
        if event.get("channel_type") != "im":
            return

        # Validate and parse event
        slack_event = validate_slack_event(event)

        correlation_ctx = create_slack_correlation_context(
            user_id=slack_event.user_id,
            channel_id=slack_event.channel_id,
            event_type="direct_message",
        )

        log_with_context(
            logger,
            "info",
            "Processing direct message",
            correlation_ctx,
            text_length=len(slack_event.text),
        )

        # Process the message
        slack_message = process_slack_message(slack_event)

        # Send acknowledgment
        send_slack_response(
            say, "I'm processing your question. I'll respond shortly..."
        )

        # Send to SQS
        if not send_to_sqs(slack_message):
            send_slack_response(
                say,
                "Sorry, I encountered an error processing your request. "
                "Please try again later.",
            )

        log_with_context(
            logger,
            "info",
            "Successfully handled direct message",
            correlation_ctx,
        )

    except MessageValidationException as e:
        log_with_context(
            logger,
            "warning",
            "Invalid direct message",
            correlation_ctx,
            error=str(e),
        )

    except (
        SlackEventException,
        SQSIntegrationException,
        SlackAPIException,
    ) as e:
        log_with_context(
            logger,
            "error",
            "Error handling direct message",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )
        try:
            send_slack_response(
                say, "Sorry, something went wrong. Please try again later."
            )
        except SlackAPIException:
            pass

    except Exception as e:
        log_with_context(
            logger,
            "error",
            "Unexpected error in direct message handler",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )


@app.command("/askq")
def handle_askq_command(ack, command, respond) -> None:
    """Handle /askq slash command."""
    correlation_ctx = None

    try:
        # Acknowledge the command immediately
        ack()

        # Parse the slash command
        slack_command = parse_slash_command(command)

        correlation_ctx = create_slack_correlation_context(
            user_id=slack_command.user_id,
            channel_id=slack_command.channel_id,
            event_type="slash_command",
        )

        log_with_context(
            logger,
            "info",
            "Processing /askq slash command",
            correlation_ctx,
            command=slack_command.command,
            text_length=len(slack_command.text),
        )

        # Process the command
        slack_message, slack_message_dict = process_slash_command(slack_command)

        # Send immediate acknowledgment
        respond({
            "response_type": "ephemeral",  # Only visible to the user
            "text": "🤔 I'm processing your question. I'll respond shortly...",
        })

        # Create SQS payload with the enhanced message dict
        dedup_id = create_message_deduplication_id(
            slack_message.conversation_id, slack_message.query
        )

        sqs_payload = SQSMessagePayload(
            message_body=slack_message_dict,  # Use enhanced dict with response_url
            message_group_id=slack_message.conversation_id,
            deduplication_id=dedup_id,
            queue_url=QUEUE_URL,
        )

        # Send to SQS
        response = sqs.send_message(
            QueueUrl=sqs_payload.queue_url,
            MessageBody=json.dumps(sqs_payload.message_body),
            MessageGroupId=sqs_payload.message_group_id,
            MessageDeduplicationId=sqs_payload.deduplication_id,
        )

        log_with_context(
            logger,
            "info",
            "Successfully handled /askq command",
            correlation_ctx,
            message_id=response["MessageId"],
        )

    except MessageValidationException as e:
        log_with_context(
            logger,
            "warning",
            "Invalid slash command",
            correlation_ctx,
            error=str(e),
        )
        try:
            respond({
                "response_type": "ephemeral",
                "text": "❌ Please provide a question after the command. Example: `/askq What is our remote work policy?`",
            })
        except Exception:
            pass

    except (SlackEventException, SQSIntegrationException) as e:
        log_with_context(
            logger,
            "error",
            "Error handling slash command",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )
        try:
            respond({
                "response_type": "ephemeral",
                "text": "❌ Sorry, something went wrong. Please try again later.",
            })
        except Exception:
            pass

    except Exception as e:
        log_with_context(
            logger,
            "error",
            "Unexpected error in slash command handler",
            correlation_ctx,
            error=str(e),
            error_type=type(e).__name__,
        )
        try:
            respond({
                "response_type": "ephemeral",
                "text": "❌ Sorry, something went wrong. Please try again later.",
            })
        except Exception:
            pass


def handle_test_query(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle direct API test queries without Slack integration."""
    try:
        # Parse the request body
        if isinstance(event.get("body"), str):
            body = json.loads(event["body"])
        else:
            body = event.get("body", {})

        query = body.get("query", "").strip()
        if not query:
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "error": "Missing 'query' field in request body",
                    "example": {"query": "What is our remote work policy?"}
                })
            }

        # Create a test message for the SQS queue
        test_conversation_id = f"test-{int(time.time())}"
        test_message = SlackMessage(
            query=query,
            user_id="test-user",
            channel_id="test-channel",
            thread_ts=None,
            conversation_id=test_conversation_id,
            timestamp=str(int(time.time())),
        )

        # Send to SQS for processing
        dedup_id = create_message_deduplication_id(
            test_message.conversation_id, test_message.query
        )

        sqs_payload = SQSMessagePayload(
            message_body=test_message.to_dict(),
            message_group_id=test_message.conversation_id,
            deduplication_id=dedup_id,
            queue_url=QUEUE_URL,
        )

        sqs_response = sqs.send_message(
            QueueUrl=sqs_payload.queue_url,
            MessageBody=json.dumps(sqs_payload.message_body),
            MessageGroupId=sqs_payload.message_group_id,
            MessageDeduplicationId=sqs_payload.deduplication_id,
        )

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({
                "status": "success",
                "message": "Query submitted for processing",
                "conversation_id": test_conversation_id,
                "message_id": sqs_response["MessageId"],
                "note": "This is a test endpoint. In production, responses would be sent to Slack."
            })
        }

    except Exception as e:
        logger.error(f"Test query error: {str(e)}")
        return {
            "statusCode": 500,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({
                "error": "Internal server error",
                "message": str(e)
            })
        }


def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Lambda handler for Slack events with comprehensive error handling."""
    request_id = getattr(context, "aws_request_id", None)

    # Check if this is a test query (direct API call)
    path = event.get("path", "")
    if "/test/query" in path:
        return handle_test_query(event)

    log_with_context(
        logger,
        "info",
        "Reader Lambda invoked",
        request_id=request_id,
        event_type=event.get("type", "unknown"),
    )

    try:
        slack_handler = SlackRequestHandler(app)
        response = slack_handler.handle(event, context)

        log_with_context(
            logger,
            "info",
            "Successfully processed Slack event",
            request_id=request_id,
            status_code=response.get("statusCode", 200),
        )

        return response

    except Exception as e:
        log_with_context(
            logger,
            "error",
            "Failed to process Slack event",
            request_id=request_id,
            error=str(e),
            error_type=type(e).__name__,
        )

        return {
            "statusCode": 500,
            "body": json.dumps(
                {"error": "Internal server error", "request_id": request_id}
            ),
        }
