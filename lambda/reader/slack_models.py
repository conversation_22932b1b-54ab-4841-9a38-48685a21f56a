"""
Data models and type definitions for Reader Lambda.

This module contains all dataclasses and type definitions used throughout
the Reader Lambda Slack integration implementation.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class SlackMessage:
    """Data class for Slack message information."""

    query: str
    user_id: str
    channel_id: str
    thread_ts: Optional[str]
    conversation_id: str
    timestamp: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "query": self.query,
            "user_id": self.user_id,
            "channel_id": self.channel_id,
            "thread_ts": self.thread_ts,
            "conversation_id": self.conversation_id,
            "timestamp": self.timestamp,
        }

    @property
    def is_direct_message(self) -> bool:
        """Check if this is a direct message."""
        return self.channel_id.startswith("D")

    @property
    def is_thread_reply(self) -> bool:
        """Check if this is a thread reply."""
        return self.thread_ts is not None


@dataclass
class SlackEvent:
    """Data class for incoming Slack events."""

    event_type: str
    user_id: str
    channel_id: str
    text: str
    timestamp: str
    thread_ts: Optional[str] = None
    bot_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "event_type": self.event_type,
            "user_id": self.user_id,
            "channel_id": self.channel_id,
            "text": self.text,
            "timestamp": self.timestamp,
            "thread_ts": self.thread_ts,
            "bot_id": self.bot_id,
        }

    @property
    def is_from_bot(self) -> bool:
        """Check if the event is from a bot."""
        return self.bot_id is not None


@dataclass
class SlackCommand:
    """Data class for Slack slash command information."""

    command: str
    text: str
    user_id: str
    user_name: str
    channel_id: str
    channel_name: str
    team_id: str
    team_domain: str
    response_url: str
    trigger_id: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "command": self.command,
            "text": self.text,
            "user_id": self.user_id,
            "user_name": self.user_name,
            "channel_id": self.channel_id,
            "channel_name": self.channel_name,
            "team_id": self.team_id,
            "team_domain": self.team_domain,
            "response_url": self.response_url,
            "trigger_id": self.trigger_id,
        }

    @property
    def conversation_id(self) -> str:
        """Generate conversation ID for slash command."""
        return f"cmd-{self.user_id}-{self.trigger_id}"

    @property
    def is_private_command(self) -> bool:
        """Check if this should be a private response."""
        return True  # Slash commands are typically private


@dataclass
class SQSMessagePayload:
    """Data class for SQS message payload structure."""

    message_body: Dict[str, Any]
    message_group_id: str
    deduplication_id: str
    queue_url: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for SQS sending."""
        return {
            "message_body": self.message_body,
            "message_group_id": self.message_group_id,
            "deduplication_id": self.deduplication_id,
            "queue_url": self.queue_url,
        }
