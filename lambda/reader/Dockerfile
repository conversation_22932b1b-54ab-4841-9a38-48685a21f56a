FROM public.ecr.aws/lambda/python:3.11

# Copy requirements.txt
COPY src/requirements.txt ${LAMBDA_TASK_ROOT}

# Install the dependencies
RUN pip install -r ${LAMBDA_TASK_ROOT}/requirements.txt

# Copy application code
COPY src/app.py ${LAMBDA_TASK_ROOT}
COPY exceptions.py ${LAMBDA_TASK_ROOT}
COPY logger_config.py ${LAMBDA_TASK_ROOT}
COPY slack_models.py ${LAMBDA_TASK_ROOT}

# Set the handler
CMD ["app.handler"] 