# Slack Slash Command Setup Guide

## 🎯 **Overview**

This guide shows how to configure your Slack app to use the `/askq` slash command instead of channel mentions. Slash commands provide a much better user experience:

✅ **Private responses** - only you see the command and response  
✅ **No channel noise** - doesn't disturb other users  
✅ **Clean interface** - dedicated command experience  
✅ **Easy to find** - responses are contextually linked  

## 🔧 **Slack App Configuration**

### **Step 1: Access Your Slack App**

1. Go to [Slack API Dashboard](https://api.slack.com/apps)
2. Select your existing app or create a new one
3. Navigate to your app's configuration

### **Step 2: Configure Slash Commands**

1. **Go to "Slash Commands"** in the left sidebar
2. **Click "Create New Command"**
3. **Configure the command:**

   ```
   Command: /askq
   Request URL: https://your-api-gateway-url/slack/commands
   Short Description: Ask a question to the AI knowledge assistant
   Usage Hint: [your question here]
   ```

4. **Save the command**

### **Step 3: Update OAuth Scopes**

1. **Go to "OAuth & Permissions"** in the left sidebar
2. **Add these Bot Token Scopes:**
   ```
   commands          # Required for slash commands
   chat:write        # Send messages
   chat:write.public # Send messages to channels the app isn't in
   ```

3. **Reinstall the app** to your workspace if prompted

### **Step 4: Get Your Endpoints**

After deploying your infrastructure, you'll get these endpoints:

```bash
# Deploy and get endpoints
make deploy ENV=dev

# Your endpoints will be:
API Gateway URL: https://abc123.execute-api.us-east-1.amazonaws.com/v1/
Slash Commands:   https://abc123.execute-api.us-east-1.amazonaws.com/v1/slack/commands
Events API:       https://abc123.execute-api.us-east-1.amazonaws.com/v1/slack/events
```

## 📱 **User Experience**

### **Using the Slash Command**

Users can now ask questions privately:

```
/askq What is our remote work policy?
/askq How do I submit expense reports?
/askq What are the company holidays this year?
```

### **Response Flow**

1. **User types:** `/askq What is our remote work policy?`
2. **Immediate response:** "🤔 I'm processing your question. I'll respond shortly..."
3. **AI processes** the question using RAG pipeline
4. **Final response** appears only to the user with:
   - Comprehensive answer
   - Source citations
   - Rich formatting

## 🔄 **Migration Strategy**

### **Option 1: Slash Commands Only**
- Remove event subscriptions for mentions
- Use only `/askq` command
- Cleanest user experience

### **Option 2: Hybrid Approach**
- Keep both slash commands and mentions
- Users can choose their preferred method
- Gradual migration path

### **Option 3: Gradual Migration**
1. Deploy slash commands alongside existing mentions
2. Announce the new `/askq` command to users
3. Monitor usage patterns
4. Eventually deprecate mention-based interactions

## ⚙️ **Technical Implementation**

### **Architecture Changes**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User types    │    │   API Gateway   │    │  Reader Lambda  │
│   /askq question│───▶│  /slack/commands│───▶│ Slash Handler   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Private Response│◀───│  Writer Lambda  │◀───│   SQS Queue     │
│ (Ephemeral)     │    │ Response URL    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Key Differences**

| Feature | Channel Mentions | Slash Commands |
|---------|------------------|----------------|
| **Visibility** | Public to channel | Private to user |
| **Response Method** | Channel message | Response URL |
| **Threading** | Uses threads | No threading |
| **Discoverability** | Visible to all | Hidden from others |
| **Professional Look** | Casual chat | Command interface |

## 🚀 **Deployment**

### **Deploy with Slash Command Support**

```bash
# Set environment variables
export SLACK_BOT_TOKEN="xoxb-your-bot-token"
export SLACK_SIGNING_SECRET="your-signing-secret"
export AWS_ACCOUNT="************"

# Deploy the infrastructure
make deploy ENV=dev

# Get the slash command endpoint
make outputs ENV=dev | grep SlackCommandsEndpoint
```

### **Update Slack App Configuration**

1. Copy the `SlackCommandsEndpoint` URL
2. Update your Slack app's slash command configuration
3. Test the command: `/askq test question`

## 🧪 **Testing**

### **Test the Slash Command**

1. **In any Slack channel or DM:**
   ```
   /askq What is our company mission?
   ```

2. **Expected behavior:**
   - Immediate acknowledgment message (only you see it)
   - Processing message appears
   - Final answer appears privately to you
   - Rich formatting with sources

### **Troubleshooting**

| Issue | Solution |
|-------|----------|
| Command not found | Check slash command is created in Slack app |
| Permission denied | Verify OAuth scopes include `commands` |
| No response | Check CloudWatch logs for Lambda errors |
| Public response | Ensure `response_type: "ephemeral"` is set |

## 📊 **Benefits Summary**

### **For Users**
- ✅ Private, focused interaction
- ✅ No channel disruption
- ✅ Professional command interface
- ✅ Easy to find responses

### **For Administrators**
- ✅ Reduced channel noise
- ✅ Better user adoption
- ✅ Cleaner audit trails
- ✅ More professional appearance

### **For the System**
- ✅ Cleaner conversation tracking
- ✅ Better response targeting
- ✅ Reduced API calls
- ✅ Improved user experience metrics

## 🎉 **Ready to Use!**

Your AI knowledge assistant now supports both:
- **Channel mentions** (existing functionality)
- **Slash commands** (new, improved experience)

Users can choose their preferred interaction method, with slash commands providing the best experience for focused Q&A sessions.
