# CloudFormation Deployment Option

## Overview

The Knowledge Retrieval System now supports **dual deployment methods**:

1. **CDK Deployment** (Original) - Uses AWS CDK for infrastructure as code
2. **CloudFormation Deployment** (New) - Uses native CloudFormation templates generated from CDK

This provides flexibility for different deployment environments and CI/CD pipeline requirements.

## Why CloudFormation Deployment?

### Use Cases

- **Enterprise Environments**: Where CDK runtime dependencies are restricted
- **CI/CD Pipelines**: That require native CloudFormation support
- **Compliance Requirements**: Need explicit CloudFormation templates for auditing
- **Template Sharing**: Want to distribute infrastructure templates without CDK dependencies
- **Debugging**: Prefer CloudFormation-native debugging tools

### Benefits

✅ **No CDK Runtime Dependencies**: Only requires AWS CLI and Python  
✅ **Explicit Templates**: Generated CloudFormation templates are visible and auditable  
✅ **Standard AWS Tooling**: Uses native CloudFormation APIs and tools  
✅ **CI/CD Friendly**: Integrates with any CloudFormation-aware pipeline  
✅ **Template Reusability**: Generated templates can be shared and reused  
✅ **Rollback Control**: Native CloudFormation rollback mechanisms  

## Quick Start

### 1. Setup CloudFormation Environment
```bash
# One-time setup
make cfn-setup
```

### 2. Deploy with CloudFormation
```bash
# Set required environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************

# Deploy everything with CloudFormation
make all-cfn ENV=dev
```

### 3. Interactive Deployment
```bash
# Use the interactive deployment script
./scripts/deploy-with-cloudformation.sh
```

## Available Commands

| Command | Description |
|---------|-------------|
| `make cfn-setup` | Setup CloudFormation environment (one-time) |
| `make cfn-generate ENV=dev` | Generate CloudFormation templates from CDK |
| `make cfn-validate ENV=dev` | Validate generated templates |
| `make cfn-package ENV=dev` | Package templates with S3 assets |
| `make cfn-deploy ENV=dev` | Deploy using CloudFormation |
| `make cfn-update ENV=dev` | Update existing stack |
| `make cfn-destroy ENV=dev` | Destroy CloudFormation stack |
| `make cfn-status ENV=dev` | Show stack status |
| `make all-cfn ENV=dev` | Complete CloudFormation deployment |

## Deployment Process

### Step 1: Environment Setup
The `cfn-setup` command:
- Validates prerequisites (AWS CLI, Python, CDK)
- Creates CloudFormation directories
- Sets up S3 bucket for artifacts
- Configures lifecycle policies

### Step 2: Template Generation
The `cfn-generate` command:
- Uses CDK to synthesize CloudFormation templates
- Outputs templates to `infrastructure/cfn/templates/`
- Maintains all CDK functionality and configurations

### Step 3: Template Packaging
The `cfn-package` command:
- Uploads Lambda code and assets to S3
- Updates template references to S3 locations
- Creates packaged templates in `infrastructure/cfn/packaged/`

### Step 4: Stack Deployment
The `cfn-deploy` command:
- Deploys packaged CloudFormation templates
- Handles stack dependencies and ordering
- Monitors deployment progress
- Reports stack outputs

## Directory Structure

```
infrastructure/cfn/
├── README.md                    # CloudFormation-specific documentation
├── deploy-cfn.py               # Python deployment script
├── setup-cfn-env.sh           # Environment setup script
├── parameters/                 # Environment-specific parameters
│   ├── dev.json               # Development parameters
│   └── prod.json              # Production parameters
├── templates/                 # Generated CloudFormation templates
│   └── (auto-generated)
└── packaged/                  # Packaged templates with S3 references
    └── (auto-generated)
```

## Configuration

### Parameter Files
Environment-specific parameters are stored in JSON files:

**Development (`parameters/dev.json`):**
```json
{
  "Environment": "dev",
  "LambdaMemorySize": "512",
  "LambdaTimeout": "30",
  "OpenSearchInstanceType": "t3.small.search"
}
```

**Production (`parameters/prod.json`):**
```json
{
  "Environment": "prod",
  "LambdaMemorySize": "1024",
  "LambdaTimeout": "60",
  "OpenSearchInstanceType": "m6g.large.search"
}
```

### Environment Variables
Same environment variables as CDK deployment:
```bash
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************
export AWS_REGION=us-east-1
```

## Comparison: CDK vs CloudFormation

| Aspect | CDK Deployment | CloudFormation Deployment |
|--------|----------------|---------------------------|
| **Prerequisites** | Node.js, CDK, AWS CLI | AWS CLI, Python |
| **Template Visibility** | Generated on-the-fly | Explicit files available |
| **Debugging** | CDK-specific tools | Standard CloudFormation tools |
| **CI/CD Integration** | CDK-aware pipelines | Any CloudFormation pipeline |
| **Deployment Speed** | Faster (direct synthesis) | Slightly slower (generation step) |
| **Template Reuse** | Limited to CDK environments | High (standard CloudFormation) |
| **Rollback** | CDK managed | CloudFormation managed |
| **Learning Curve** | CDK knowledge required | Standard CloudFormation knowledge |

## Migration Between Methods

### From CDK to CloudFormation
1. Export current CDK stack outputs
2. Generate CloudFormation templates: `make cfn-generate ENV=prod`
3. Deploy CloudFormation stack: `make cfn-deploy ENV=prod`
4. Verify functionality and outputs match
5. Optionally destroy CDK stack: `make destroy ENV=prod`

### From CloudFormation to CDK
1. Export CloudFormation stack outputs
2. Deploy CDK stack with same configuration: `make deploy ENV=prod`
3. Verify functionality and outputs match
4. Optionally destroy CloudFormation stack: `make cfn-destroy ENV=prod`

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Deploy with CloudFormation
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup CloudFormation environment
        run: make cfn-setup
      - name: Deploy with CloudFormation
        run: make cfn-deploy ENV=prod
        env:
          AWS_ACCOUNT: ${{ secrets.AWS_ACCOUNT }}
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_SIGNING_SECRET: ${{ secrets.SLACK_SIGNING_SECRET }}
```

### AWS CodePipeline Integration
The generated CloudFormation templates can be used directly in AWS CodePipeline with CloudFormation actions.

## Troubleshooting

### Common Issues

1. **Template Generation Fails**
   ```bash
   # Ensure CDK dependencies are installed
   cd infrastructure && npm install
   ```

2. **S3 Bucket Access Issues**
   ```bash
   # Check AWS permissions
   aws s3 ls
   ```

3. **Stack Deployment Fails**
   ```bash
   # Check CloudFormation events
   make cfn-status ENV=dev
   ```

### Debug Commands
```bash
# View generated templates
ls -la infrastructure/cfn/templates/

# Validate template syntax
aws cloudformation validate-template --template-body file://template.json

# Monitor stack events
aws cloudformation describe-stack-events --stack-name your-stack-name
```

## Best Practices

1. **Version Control**: Commit generated templates for reproducibility
2. **Parameter Management**: Use separate parameter files for each environment
3. **S3 Lifecycle**: The setup automatically configures S3 lifecycle policies
4. **Template Validation**: Always run `cfn-validate` before deployment
5. **Stack Monitoring**: Use `cfn-status` to monitor deployment progress

## Support

For CloudFormation-specific issues:
1. Check the [CloudFormation README](infrastructure/cfn/README.md)
2. Review AWS CloudFormation documentation
3. Use AWS CloudFormation console for detailed error messages
4. Check CloudWatch logs for Lambda function issues

The CloudFormation deployment maintains full feature parity with the CDK deployment while providing additional flexibility for enterprise and CI/CD environments.
