# Updated Architecture Diagram - API Gateway Migration

## High-Level Architecture Flow

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           AWS Cloud Environment                                     │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    Enhanced Request Processing                               │   │
│  │                                                                             │   │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │   │
│  │  │   Slack     │───▶│ API Gateway │───▶│   Reader    │───▶│ SQS FIFO    │  │   │
│  │  │             │    │             │    │   Lambda    │    │   Queue     │  │   │
│  │  │             │    │ /slack/     │    │ (Slack Bot) │    │             │  │   │
│  │  │             │    │ events      │    │             │    │             │  │   │
│  │  └─────────────┘    │ /slack/     │    └─────────────┘    └─────────────┘  │   │
│  │                     │ interactive │                              │         │   │
│  │                     └─────────────┘                              ▼         │   │
│  │                                                         ┌─────────────┐    │   │
│  │                     ┌─────────────────────────────────▶ │   Writer    │    │   │
│  │                     │                                   │   Lambda    │    │   │
│  │                     │                                   │             │    │   │
│  │                     │                                   └─────────────┘    │   │
│  └─────────────────────┼─────────────────────────────────────────────────────┘   │
│                        │                                          │               │
│  ┌─────────────────────┼──────────────────────────────────────────┼─────────────┐ │
│  │                     │        Data Storage & Retrieval          │             │ │
│  │                     │                                          ▼             │ │
│  │  ┌─────────────┐    │    ┌─────────────┐    ┌─────────────┐ ┌─────────────┐  │ │
│  │  │  DynamoDB   │◀───┼───▶│  Amazon Q   │    │ OpenSearch  │ │   Amazon    │  │ │
│  │  │(Conversation│    │    │  Business   │    │   Vector    │ │     S3      │  │ │
│  │  │  History)   │    │    │(Confluence/ │    │  Database   │ │ (Document   │  │ │
│  │  │             │    │    │ SharePoint) │    │             │ │  Storage)   │  │ │
│  │  └─────────────┘    │    └─────────────┘    └─────────────┘ └─────────────┘  │ │
│  │                     │           ▲                   ▲              │        │ │
│  │                     │           │                   │              ▼        │ │
│  │                     │           │                   │      ┌─────────────┐  │ │
│  │                     │           │                   │      │  Ingestion  │  │ │
│  │                     │           │                   │      │   Lambda    │  │ │
│  │                     │           │                   │      │             │  │ │
│  │                     │           │                   │      └─────────────┘  │ │
│  └─────────────────────┼───────────┼───────────────────┼─────────────────────┘ │
│                        │           │                   │                       │
│  ┌─────────────────────┼───────────┼───────────────────┼─────────────────────┐ │
│  │                     │    AI/ML Processing           │                     │ │
│  │                     │                               │                     │ │
│  │                     │    ┌─────────────┐    ┌─────────────┐              │ │
│  │                     └───▶│   Amazon    │    │   Amazon    │              │ │
│  │                          │   Bedrock   │    │ Comprehend  │              │ │
│  │                          │ (Claude 3)  │    │             │              │ │
│  │                          │             │    │             │              │ │
│  │                          └─────────────┘    └─────────────┘              │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                               │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                    Security & Monitoring                                 │ │
│  │                                                                         │ │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                  │ │
│  │  │ CloudWatch  │    │     IAM     │    │    X-Ray    │                  │ │
│  │  │ (Logs &     │    │   Roles     │    │  Tracing    │                  │ │
│  │  │  Metrics)   │    │             │    │             │                  │ │
│  │  └─────────────┘    └─────────────┘    └─────────────┘                  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘

External Data Sources:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Confluence  │    │ SharePoint  │    │   Custom    │
│             │    │             │    │    Data     │
│             │    │             │    │  Sources    │
└─────────────┘    └─────────────┘    └─────────────┘
```

## Key Changes from ALB to API Gateway

### 1. Entry Point Change
- **Before**: Slack → Application Load Balancer → Reader Lambda
- **After**: Slack → API Gateway → Reader Lambda

### 2. API Gateway Endpoints
- `POST /slack/events` - Handles Slack Events API
- `POST /slack/interactive` - Handles Slack interactive components
- `POST /slack/commands` - Handles Slack slash commands (/askq)

### 3. Enhanced Features
- Built-in request/response transformation
- CloudWatch logging and monitoring
- Request validation and throttling
- Better cost efficiency (pay-per-request vs fixed ALB costs)

## Data Flow Description

1. **User Interaction**: User sends message/mention in Slack
2. **API Gateway**: Receives HTTP request from Slack, transforms it, and forwards to Reader Lambda
3. **Reader Lambda**: Processes Slack event, validates, and sends to SQS FIFO queue
4. **Writer Lambda**: Triggered by SQS message, orchestrates the knowledge retrieval process:
   - Queries conversation history from DynamoDB
   - Searches Amazon Q Business (Confluence/SharePoint)
   - Searches OpenSearch vector database
   - Sends context + query to Amazon Bedrock (Claude 3)
   - Stores conversation history in DynamoDB
   - Sends response back to Slack
5. **Document Ingestion**: Custom documents uploaded to S3 trigger Ingestion Lambda for vector processing

## Monitoring and Security

- **CloudWatch**: Comprehensive logging and metrics for all components
- **IAM**: Least privilege access controls
- **X-Ray**: Distributed tracing for debugging
- **API Gateway**: Built-in monitoring, throttling, and security features

## Benefits of API Gateway Migration

1. **Cost Optimization**: Pay-per-request pricing vs fixed ALB costs
2. **Better Integration**: Native AWS service integration
3. **Enhanced Monitoring**: Built-in CloudWatch integration
4. **Simplified Management**: No need for target groups or listeners
5. **Better Security**: Built-in request validation and throttling
6. **Easier Scaling**: Automatic scaling without configuration 