# Deployment Guide - Intelligent Knowledge Retrieval System

This guide provides step-by-step instructions for deploying the complete AWS infrastructure using either CDK or CloudFormation deployment methods.

## Deployment Options

This system supports two deployment methods:

1. **CDK Deployment** (Recommended for development) - Uses AWS CDK for infrastructure as code
2. **CloudFormation Deployment** (Recommended for production/CI-CD) - Uses native CloudFormation templates

## Quick Start

### CDK Deployment (Default)
For the impatient, here's the minimal setup:

```bash
# 1. Set required environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************

# 2. Deploy everything with CDK
make all ENV=dev
```

### CloudFormation Deployment
For environments where CDK is not available or preferred:

```bash
# 1. Set required environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************

# 2. Setup CloudFormation environment
make cfn-setup

# 3. Deploy everything with CloudFormation
make all-cfn ENV=dev
```

## Prerequisites

### Required Tools
- **AWS CLI v2** - Configured with appropriate permissions
- **AWS CDK v2** - Latest version
- **Docker** - For building Lambda container images
- **Node.js 18+** - For CDK dependencies
- **Python 3.11+** - For Lambda functions
- **Make** - For running deployment commands
- **jq** (optional) - For prettier JSON output

### AWS Permissions Required
Your AWS credentials need the following permissions:
- **CloudFormation**: Full access for stack management
- **Lambda**: Create, update, delete functions and layers
- **API Gateway**: Create and manage REST APIs
- **IAM**: Create roles and policies for services
- **EC2**: VPC, subnet, and security group management (if using existing VPC)
- **DynamoDB**: Create and manage tables
- **OpenSearch**: Create and manage domains
- **S3**: Create buckets and manage objects
- **SQS**: Create and manage queues
- **Bedrock**: Access to Claude 3 and Titan models
- **ECR**: Push and pull Docker images

### Slack App Configuration
1. Create a new Slack App at https://api.slack.com/apps
2. Configure OAuth & Permissions with required scopes:
   - `app_mentions:read`
   - `channels:history`
   - `chat:write`
   - `im:history`
   - `im:read`
   - `im:write`
3. Enable Event Subscriptions (URL will be provided after deployment)
4. Install the app to your workspace

## Configuration Options

### Environment Variables

**Required:**
```bash
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret
export AWS_ACCOUNT=************
```

**Optional (with defaults):**
```bash
export AWS_REGION=us-east-1
export LAMBDA_MEMORY_SIZE=512
export LAMBDA_TIMEOUT=30
export OPENSEARCH_INSTANCE_TYPE=t3.small.search
export DYNAMODB_BILLING_MODE=PAY_PER_REQUEST
```

**For Existing VPC:**
```bash
export VPC_ID=vpc-********
export PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890
export PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij
```

## Deployment Scenarios

### Scenario 1: New VPC (Recommended for Development)

This is the simplest deployment option where CDK creates all networking resources.

```bash
# 1. Set required environment variables
export SLACK_BOT_TOKEN=xoxb-your-bot-token
export SLACK_SIGNING_SECRET=your-signing-secret

# 2. Deploy to development environment
make deploy ENV=dev

# 3. Check deployment status
make status ENV=dev

# 4. Get stack outputs (including API Gateway URL)
make outputs ENV=dev
```

### Scenario 2: Existing VPC (Production Recommended)

Use this for production deployments where you have existing VPC infrastructure.

#### Option A: Interactive Setup
```bash
# Run the interactive VPC configuration script
./scripts/setup-vpc-config.sh

# Follow the prompts to configure your VPC and subnets
# The script will validate everything and generate configuration

# Deploy using the generated configuration
set -a && source infrastructure/env/.env.custom-vpc && set +a
make deploy ENV=prod
```

#### Option B: Manual Configuration
```bash
# Set VPC configuration
export VPC_ID=vpc-********
export PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890,subnet-11111
export PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij,subnet-22222

# Deploy with existing VPC
make deploy ENV=prod \
  VPC_ID=$VPC_ID \
  PRIVATE_SUBNET_IDS="$PRIVATE_SUBNET_IDS" \
  PUBLIC_SUBNET_IDS="$PUBLIC_SUBNET_IDS"
```

#### Option C: Configuration File
```bash
# Create environment-specific configuration
cat > infrastructure/env/.env.prod << EOF
ENV=prod
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=us-west-2
VPC_ID=vpc-********
PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890
PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij
SLACK_BOT_TOKEN=xoxb-your-prod-token
SLACK_SIGNING_SECRET=your-prod-secret
LAMBDA_MEMORY_SIZE=1024
OPENSEARCH_INSTANCE_TYPE=m5.large.search
DYNAMODB_BILLING_MODE=PROVISIONED
EOF

# Deploy using configuration file
make deploy ENV=prod
```

## Makefile Commands Reference

### Core Deployment Commands

```bash
make help                    # Show all available commands
make deploy ENV=dev          # Full deployment to dev environment
make deploy-dev              # Shortcut for dev deployment
make deploy-prod             # Shortcut for prod deployment
make update ENV=dev          # Update existing stack without rebuilding
make destroy ENV=dev         # Destroy the stack (with confirmation)
```

### Build Commands

```bash
make install-deps            # Install all dependencies
make build                   # Build all components (layers, images, code)
make build-layers            # Build Lambda layers only
make build-images            # Build Docker images only
make prepare-lambda-code     # Prepare Lambda function code
```

### Infrastructure Commands

```bash
make bootstrap               # Bootstrap CDK (one-time setup)
make synth ENV=dev           # Synthesize CloudFormation templates
make diff ENV=dev            # Show differences from current deployment
make outputs ENV=dev         # Display stack outputs
make status ENV=dev          # Show deployment status
```

### Development Commands

```bash
make test                    # Run all tests
make lint                    # Run code linting
make format                  # Format code with Black
make clean                   # Clean build artifacts
make logs ENV=dev            # Show available log groups
```

### Utility Commands

```bash
make validate-config         # Validate required configuration
make check-aws              # Check AWS credentials and permissions
make example-config         # Show configuration examples
make docs                   # Generate documentation
```

## Step-by-Step Deployment Process

### Step 1: Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd awsAIplatform

# Install dependencies
make install-deps

# Configure AWS credentials (if not already done)
aws configure
```

### Step 2: Environment Configuration

Choose one of these approaches:

**For Development (New VPC):**
```bash
export SLACK_BOT_TOKEN=xoxb-your-dev-token
export SLACK_SIGNING_SECRET=your-dev-secret
```

**For Production (Existing VPC):**
```bash
# Run interactive setup
./scripts/setup-vpc-config.sh

# Or manually set variables
export VPC_ID=vpc-********
export PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890
export PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij
export SLACK_BOT_TOKEN=xoxb-your-prod-token
export SLACK_SIGNING_SECRET=your-prod-secret
```

### Step 3: Validate Configuration

```bash
# Check AWS credentials and configuration
make check-aws

# Validate all required configuration
make validate-config
```

### Step 4: Deploy Infrastructure

```bash
# For development
make deploy ENV=dev

# For production
make deploy ENV=prod

# Or do everything in one command
make all ENV=dev
```

### Step 5: Configure Slack App

After deployment, you'll get the API Gateway URL in the outputs:

```bash
# Get the API Gateway URL
make outputs ENV=dev

# Look for output like:
# SlackEventsEndpoint = https://abc123.execute-api.us-east-1.amazonaws.com/v1/slack/events
```

Update your Slack App configuration:
1. Go to your Slack App settings
2. Navigate to "Event Subscriptions"
3. Set the Request URL to the `SlackEventsEndpoint` from the outputs
4. Slack will verify the endpoint automatically

### Step 6: Test the Deployment

```bash
# Check deployment status
make status ENV=dev

# Monitor logs
make logs ENV=dev

# Test in Slack by mentioning your bot
@your-bot-name What is AWS Lambda?
```

## Environment-Specific Configurations

### Development Environment
```bash
# Minimal configuration for testing
ENV=dev
LAMBDA_MEMORY_SIZE=512
LAMBDA_TIMEOUT=30
OPENSEARCH_INSTANCE_TYPE=t3.small.search
DYNAMODB_BILLING_MODE=PAY_PER_REQUEST
```

### Staging Environment
```bash
# Medium configuration for integration testing
ENV=staging
LAMBDA_MEMORY_SIZE=1024
LAMBDA_TIMEOUT=60
OPENSEARCH_INSTANCE_TYPE=t3.medium.search
DYNAMODB_BILLING_MODE=PROVISIONED
```

### Production Environment
```bash
# Full configuration for production workloads
ENV=prod
LAMBDA_MEMORY_SIZE=2048
LAMBDA_TIMEOUT=120
OPENSEARCH_INSTANCE_TYPE=m5.large.search
DYNAMODB_BILLING_MODE=PROVISIONED
VPC_ID=vpc-prod-********
PRIVATE_SUBNET_IDS=subnet-prod-1,subnet-prod-2,subnet-prod-3
PUBLIC_SUBNET_IDS=subnet-prod-pub-1,subnet-prod-pub-2,subnet-prod-pub-3
```

## Troubleshooting

### Common Issues

**1. AWS Credentials Not Configured**
```bash
# Error: Unable to locate credentials
aws configure
# or
export AWS_PROFILE=your-profile
```

**2. CDK Not Bootstrapped**
```bash
# Error: This stack uses assets, so the toolkit stack must be deployed
make bootstrap
```

**3. Slack Token Issues**
```bash
# Error: SLACK_BOT_TOKEN is required
export SLACK_BOT_TOKEN=xoxb-your-actual-token
make validate-config
```

**4. VPC Not Found**
```bash
# Error: VPC vpc-12345 not found
# Verify VPC exists in the correct region
aws ec2 describe-vpcs --vpc-ids vpc-12345
```

**5. Docker Build Fails**
```bash
# Make sure Docker is running
docker info

# Clean and rebuild
make clean
make build-images
```

### Viewing Logs

```bash
# List available log groups
make logs ENV=dev

# Tail specific log group
aws logs tail /aws/lambda/knowledge-retrieval-reader-dev --follow

# View API Gateway logs
aws logs tail /aws/apigateway/knowledge-retrieval-api-dev --follow
```

### Stack Outputs

Important outputs after deployment:

- **APIGatewayURL**: Base URL for the API Gateway
- **SlackEventsEndpoint**: URL to configure in Slack app
- **ReaderLambdaArn**: ARN of the Slack event processor
- **WriterLambdaArn**: ARN of the knowledge retrieval processor
- **DynamoDBTableName**: Name of the conversation history table
- **OpenSearchDomainEndpoint**: OpenSearch cluster endpoint
- **S3BucketName**: Document storage bucket name

## Security Best Practices

### Environment Variables
- Never commit secrets to version control
- Use different tokens for different environments
- Rotate tokens regularly
- Use AWS Secrets Manager for production secrets

### VPC Security
- Use private subnets for Lambda functions
- Ensure OpenSearch is in private subnets
- Use security groups to limit access
- Enable VPC Flow Logs for monitoring

### IAM Permissions
- Follow least privilege principle
- Use separate IAM roles for different functions
- Regularly audit permissions
- Enable CloudTrail for API call monitoring

## Cost Optimization

### Development Environment
- Use smaller instance types
- Enable pay-per-request billing for DynamoDB
- Use minimal Lambda memory allocation
- Clean up unused resources regularly

### Production Environment
- Use Reserved Instances for OpenSearch
- Consider Savings Plans for Lambda
- Implement proper data retention policies
- Monitor costs with AWS Cost Explorer

## Monitoring and Maintenance

### Regular Tasks
```bash
# Check deployment status
make status ENV=prod

# View recent logs
make logs ENV=prod

# Update stack with latest changes
make update ENV=prod

# Run tests
make test
```

### Alerts and Monitoring
The deployment includes CloudWatch alarms for:
- Lambda function errors and timeouts
- API Gateway 4XX/5XX errors
- DynamoDB throttling
- OpenSearch cluster health

## Cleanup

To completely remove the deployment:

```bash
# Destroy development environment
make destroy ENV=dev

# Destroy production environment
make destroy ENV=prod

# Clean local build artifacts
make clean
```

**Warning**: The destroy command will permanently delete all resources including data in DynamoDB and S3. Make sure to backup any important data before running this command.

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Review CloudWatch logs and metrics
3. Consult the [main documentation](README.md)
4. Check [architecture documentation](UPDATED_ARCHITECTURE_DIAGRAM.md) 