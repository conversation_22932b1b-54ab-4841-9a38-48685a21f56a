#!/bin/bash

# <PERSON>ript to discover existing VPC resources for cost optimization
# This helps identify existing security groups and VPC endpoints that can be reused

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Discovering Existing Security Groups to Avoid Duplication${NC}"
echo "=============================================================="

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI not found. Please install AWS CLI first.${NC}"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

REGION=$(aws configure get region)
if [ -z "$REGION" ]; then
    REGION="us-east-1"
    echo -e "${YELLOW}⚠️  No default region set, using us-east-1${NC}"
fi

echo -e "${GREEN}✅ AWS CLI configured for region: $REGION${NC}"
echo ""

# Function to check if VPC endpoints exist (for informational purposes)
check_vpc_endpoints() {
    echo -e "${BLUE}🔍 Checking for existing VPC endpoints (informational)...${NC}"

    # Check for SQS VPC endpoint
    SQS_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.sqs" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")

    # Check for Bedrock VPC endpoint
    BEDROCK_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.bedrock" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")

    # Check for S3 Gateway endpoint
    S3_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.s3" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")

    # Check for DynamoDB Gateway endpoint
    DYNAMODB_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.dynamodb" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")

    echo "SQS VPC Endpoint: $SQS_ENDPOINT"
    echo "Bedrock VPC Endpoint: $BEDROCK_ENDPOINT"
    echo "S3 Gateway Endpoint: $S3_ENDPOINT"
    echo "DynamoDB Gateway Endpoint: $DYNAMODB_ENDPOINT"

    echo -e "${YELLOW}💡 Note: VPC endpoints are managed separately and will be used automatically if available.${NC}"
    echo ""
}

# Function to check for existing security groups
check_security_groups() {
    echo -e "${BLUE}🔍 Checking for existing security groups...${NC}"
    
    # Look for Lambda security groups
    LAMBDA_SG=$(aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=*lambda*" \
        --query 'SecurityGroups[0].GroupId' \
        --output text 2>/dev/null || echo "None")
    
    # Look for OpenSearch security groups
    OPENSEARCH_SG=$(aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=*opensearch*,*elasticsearch*" \
        --query 'SecurityGroups[0].GroupId' \
        --output text 2>/dev/null || echo "None")
    
    echo "Lambda Security Group: $LAMBDA_SG"
    echo "OpenSearch Security Group: $OPENSEARCH_SG"
    echo ""
}

# Function to generate export commands
generate_export_commands() {
    echo -e "${BLUE}📋 Generated Export Commands${NC}"
    echo "================================"
    echo ""

    if [ "$LAMBDA_SG" != "None" ] && [ "$LAMBDA_SG" != "null" ]; then
        echo -e "${GREEN}# Security Groups (avoids duplication)${NC}"
        echo "export EXISTING_LAMBDA_SG_ID=\"$LAMBDA_SG\""
    fi

    if [ "$OPENSEARCH_SG" != "None" ] && [ "$OPENSEARCH_SG" != "null" ]; then
        echo "export EXISTING_OPENSEARCH_SG_ID=\"$OPENSEARCH_SG\""
    fi

    echo ""
    echo -e "${YELLOW}💡 Copy the export commands above and run them before deployment${NC}"
    echo -e "${YELLOW}   This will avoid creating duplicate security groups.${NC}"
}

# Function to show resource optimization
show_resource_optimization() {
    echo -e "${BLUE}🔧 Resource Optimization Summary${NC}"
    echo "=================================="

    if [ "$LAMBDA_SG" != "None" ] && [ "$LAMBDA_SG" != "null" ]; then
        echo -e "${GREEN}✅ Lambda Security Group: Reusing existing${NC}"
    else
        echo -e "${YELLOW}⚠️  Lambda Security Group: New group will be created${NC}"
    fi

    if [ "$OPENSEARCH_SG" != "None" ] && [ "$OPENSEARCH_SG" != "null" ]; then
        echo -e "${GREEN}✅ OpenSearch Security Group: Reusing existing${NC}"
    else
        echo -e "${YELLOW}⚠️  OpenSearch Security Group: New group will be created${NC}"
    fi

    echo ""
    echo -e "${BLUE}📝 VPC Endpoints${NC}"
    echo "VPC endpoints should be managed separately for better cost control and reusability."
    echo "Lambda functions will automatically use any existing VPC endpoints in the VPC."
}

# Main execution
check_vpc_endpoints
check_security_groups
generate_export_commands
show_resource_optimization

echo ""
echo -e "${BLUE}🚀 Next Steps${NC}"
echo "============="
echo "1. Copy the export commands above"
echo "2. Run them in your terminal"
echo "3. Deploy with: make deploy ENV=dev"
echo ""
echo -e "${GREEN}✅ Resource discovery complete!${NC}"
