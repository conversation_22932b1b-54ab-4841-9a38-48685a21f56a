#!/bin/bash
# Example CloudFormation deployment script
# This script demonstrates how to deploy the Knowledge Retrieval System using CloudFormation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENV=${ENV:-dev}
AWS_REGION=${AWS_REGION:-us-east-1}

echo -e "${BLUE}Knowledge Retrieval System - CloudFormation Deployment${NC}"
echo "======================================================="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to prompt for input
prompt_for_input() {
    local prompt="$1"
    local var_name="$2"
    local current_value="${!var_name}"
    
    if [ -n "$current_value" ]; then
        echo -e "${YELLOW}$prompt (current: $current_value):${NC}"
    else
        echo -e "${YELLOW}$prompt:${NC}"
    fi
    
    read -r input
    if [ -n "$input" ]; then
        export "$var_name"="$input"
    fi
}

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if ! command_exists aws; then
    echo -e "${RED}Error: AWS CLI is not installed${NC}"
    exit 1
fi

if ! command_exists make; then
    echo -e "${RED}Error: Make is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Prerequisites check passed${NC}"

# Check AWS credentials
echo -e "${BLUE}Checking AWS credentials...${NC}"
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}Error: AWS credentials are not configured${NC}"
    exit 1
fi

AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
echo -e "${GREEN}✓ AWS Account: ${AWS_ACCOUNT}${NC}"
echo -e "${GREEN}✓ AWS Region: ${AWS_REGION}${NC}"

# Prompt for required configuration
echo -e "${BLUE}Configuration Setup${NC}"
echo "==================="

prompt_for_input "Environment (dev/staging/prod)" "ENV"
prompt_for_input "AWS Region" "AWS_REGION"
prompt_for_input "Slack Bot Token" "SLACK_BOT_TOKEN"
prompt_for_input "Slack Signing Secret" "SLACK_SIGNING_SECRET"
prompt_for_input "Amazon Q Application ID (optional)" "AMAZON_Q_APPLICATION_ID"

# Optional VPC configuration
echo ""
echo -e "${BLUE}VPC Configuration (optional)${NC}"
echo "============================="
echo -e "${YELLOW}Leave empty to create a new VPC${NC}"

prompt_for_input "Existing VPC ID (optional)" "VPC_ID"
if [ -n "$VPC_ID" ]; then
    prompt_for_input "Private Subnet IDs (comma-separated)" "PRIVATE_SUBNET_IDS"
    prompt_for_input "Public Subnet IDs (comma-separated)" "PUBLIC_SUBNET_IDS"
    prompt_for_input "Existing Lambda Security Group ID (optional)" "EXISTING_LAMBDA_SG_ID"
    prompt_for_input "Existing OpenSearch Security Group ID (optional)" "EXISTING_OPENSEARCH_SG_ID"
fi

# Performance configuration
echo ""
echo -e "${BLUE}Performance Configuration${NC}"
echo "========================="

case "$ENV" in
    "prod")
        LAMBDA_MEMORY_SIZE=${LAMBDA_MEMORY_SIZE:-2048}
        LAMBDA_TIMEOUT=${LAMBDA_TIMEOUT:-120}
        OPENSEARCH_INSTANCE_TYPE=${OPENSEARCH_INSTANCE_TYPE:-m5.large.search}
        ;;
    "staging")
        LAMBDA_MEMORY_SIZE=${LAMBDA_MEMORY_SIZE:-1024}
        LAMBDA_TIMEOUT=${LAMBDA_TIMEOUT:-60}
        OPENSEARCH_INSTANCE_TYPE=${OPENSEARCH_INSTANCE_TYPE:-t3.medium.search}
        ;;
    *)
        LAMBDA_MEMORY_SIZE=${LAMBDA_MEMORY_SIZE:-512}
        LAMBDA_TIMEOUT=${LAMBDA_TIMEOUT:-30}
        OPENSEARCH_INSTANCE_TYPE=${OPENSEARCH_INSTANCE_TYPE:-t3.small.search}
        ;;
esac

prompt_for_input "Lambda Memory Size (MB)" "LAMBDA_MEMORY_SIZE"
prompt_for_input "Lambda Timeout (seconds)" "LAMBDA_TIMEOUT"
prompt_for_input "OpenSearch Instance Type" "OPENSEARCH_INSTANCE_TYPE"

# Summary
echo ""
echo -e "${BLUE}Deployment Summary${NC}"
echo "=================="
echo "Environment: $ENV"
echo "AWS Account: $AWS_ACCOUNT"
echo "AWS Region: $AWS_REGION"
echo "Lambda Memory: ${LAMBDA_MEMORY_SIZE}MB"
echo "Lambda Timeout: ${LAMBDA_TIMEOUT}s"
echo "OpenSearch Instance: $OPENSEARCH_INSTANCE_TYPE"

if [ -n "$VPC_ID" ]; then
    echo "VPC ID: $VPC_ID"
    echo "Using existing VPC"
else
    echo "VPC: Will create new VPC"
fi

echo ""
echo -e "${YELLOW}Do you want to proceed with the deployment? (y/N)${NC}"
read -r confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Export all variables for make
export ENV AWS_ACCOUNT AWS_REGION
export SLACK_BOT_TOKEN SLACK_SIGNING_SECRET
export LAMBDA_MEMORY_SIZE LAMBDA_TIMEOUT OPENSEARCH_INSTANCE_TYPE
if [ -n "$AMAZON_Q_APPLICATION_ID" ]; then
    export AMAZON_Q_APPLICATION_ID
fi
if [ -n "$VPC_ID" ]; then
    export VPC_ID PRIVATE_SUBNET_IDS PUBLIC_SUBNET_IDS
    if [ -n "$EXISTING_LAMBDA_SG_ID" ]; then
        export EXISTING_LAMBDA_SG_ID
    fi
    if [ -n "$EXISTING_OPENSEARCH_SG_ID" ]; then
        export EXISTING_OPENSEARCH_SG_ID
    fi
fi

# Start deployment
echo ""
echo -e "${BLUE}Starting CloudFormation deployment...${NC}"
echo "======================================"

# Step 1: Setup CloudFormation environment
echo -e "${BLUE}Step 1: Setting up CloudFormation environment...${NC}"
if ! make cfn-setup; then
    echo -e "${RED}Failed to setup CloudFormation environment${NC}"
    exit 1
fi

# Step 2: Generate and validate templates
echo -e "${BLUE}Step 2: Generating CloudFormation templates...${NC}"
if ! make cfn-generate ENV="$ENV"; then
    echo -e "${RED}Failed to generate CloudFormation templates${NC}"
    exit 1
fi

echo -e "${BLUE}Step 3: Validating CloudFormation templates...${NC}"
if ! make cfn-validate ENV="$ENV"; then
    echo -e "${RED}CloudFormation template validation failed${NC}"
    exit 1
fi

# Step 3: Deploy
echo -e "${BLUE}Step 4: Deploying with CloudFormation...${NC}"
if ! make cfn-deploy ENV="$ENV" AWS_ACCOUNT="$AWS_ACCOUNT" AWS_REGION="$AWS_REGION"; then
    echo -e "${RED}CloudFormation deployment failed${NC}"
    exit 1
fi

# Success
echo ""
echo -e "${GREEN}✅ CloudFormation deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Get the API Gateway URL:"
echo "   make outputs ENV=$ENV"
echo ""
echo "2. Configure your Slack app with the Events URL"
echo ""
echo "3. Test the deployment by mentioning your bot in Slack"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "- Check status: make cfn-status ENV=$ENV"
echo "- View logs: make logs ENV=$ENV"
echo "- Update stack: make cfn-update ENV=$ENV"
echo "- Destroy stack: make cfn-destroy ENV=$ENV"
